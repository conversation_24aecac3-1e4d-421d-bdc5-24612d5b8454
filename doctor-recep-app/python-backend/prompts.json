{"outpatient": {"format": "structured EMR", "style": "consultation summary", "ai_instruction": "You are a medical assistant AI. Convert the following consultation notes into a structured EMR in the format below. Your response must adhere to Clinical Documentation Integrity (CDI) principles, ensuring it is complete, accurate, and consistent. Use precise clinical terminology suitable for SNOMED CT mapping, especially for complaints and findings.Assign the appropriate ICD-10 code to the diagnosis. IMPORTANT: Provide ONLY the final structured output - do not include any instructional text, explanations, or meta-commentary.", "template_structure": "\nConsultation Summary:\n  Patient Details:\n    - Name: [Full Name or Initials if unknown]\n    - Age: [in years]\n    - Gender: [Male / Female / Other]\n    - Date of Consultation: [DD-MM-YYYY]\n    - Time: [HH:MM AM/PM]\n\n  Chief Complaints:\n    - [Symptom 1: duration]\n    - [Symptom 2: duration]\n    - ...\n\n  History of Present Illness:\n    - [Detailed narrative of symptom onset, progression, any self-medication, relevant context]\n\n  Past Medical History:\n    - [Diabetes (5 yrs), Hypertension (3 yrs), No known allergies]\n    - [Include surgical history or prior major illnesses]\n\n  Examination Findings:\n    - Vitals: BP [__], Pulse [__], Temp [__], SPO2 [__]\n    - General Examination: [Patient alert, oriented, mild pallor]\n    - Systemic Exam: \n        - Respiratory: [Clear breath sounds]\n        - Cardiovascular: [Normal heart sounds]\n        - Abdomen: [Soft, non-tender]\n        - Neuro: [Normal reflexes]\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - [Primary diagnosis with its ICD-10 Code - Acute viral pharyngitis (J02.9)]\n    - [Differential if applicable]\n\n  Investigations Ordered:\n    - [Test 1]\n    - [Test 2]\n    - [Mention old reports if referred]\n\n  Prescription:\n    - [Drug Name] – [Dose] – [Frequency] – [Duration]\n    - [Any supplements / injections / inhalers]\n    - [Advice: dietary, lifestyle, red flags]\n\n  Follow-Up Plan:\n    - [Review after 3 days / When reports ready / Emergency if symptoms worsen]\n\n  Notes:\n    - [Optional remarks: referral to specialist, limitations of diagnosis, patient refusal, consent taken, etc.]\n\n  Doctor ID:\n    - [Dr. Name / ID / Signature token]\n", "instructions": "\nCreate a structured EMR consultation summary, ensuring it meets Clinical Documentation Integrity (CDI) standards.\n- Focus on current symptoms and immediate treatment\n- Keep prescriptions clear and dosage-specific\n- Assign an ICD-10 code to the provisional diagnosis\n- Include follow-up instructions\n- Maintain professional medical terminology\n", "sections": ["patient_details", "chief_complaints", "history_present_illness", "past_medical_history", "examination_findings", "provisional_diagnosis", "investigations_ordered", "prescription", "follow_up_plan", "notes", "doctor_id"]}, "discharge": {"format": "discharge summary", "style": "structured hospital discharge", "ai_instruction": "You are a medical assistant AI trained to generate discharge summaries for Indian hospitals. Convert the following clinical data into a structured discharge summary. Your response must adhere to Clinical Documentation Integrity (CDI) principles.Ensure that clinical narratives, especially in the 'Hospital Course' and 'Investigations' sections, use structured terminology consistent with SNOMED CT principles for data interoperability. Assign the appropriate ICD-10 code to the final diagnosis. Use clear, clinical, grammatically correct English and write in long-form where appropriate. Assume the reader is another doctor. IMPORTANT: Provide ONLY the final structured discharge summary - do not include any instructional text, explanations, or meta-commentary.", "template_structure": "\nDISCHARGE SUMMARY\n\nPatient Details\nName: [Full Name]\nAge / Sex: [## / M/F/O]\nHospital / IP No.: [########]\nAdmission Date: [DD-MM-YYYY]\nDischarge Date: [DD-MM-YYYY]\nConsultant / Department: [Dr. XYZ / General Medicine]\n\nPresenting Complaints\n[Primary complaints with duration - Fever and cough for 3 days]\n\nHistory of Present Illness\n[Chronological narrative of symptoms, progression, prior treatment]\n\nPast Medical / Surgical History\n[DM/HTN/CKD/CAD or previous surgeries if any]\n\nAllergies\n[Drug / Food / Environmental — if known]\n\nPersonal / Family History\n[Smoking / Alcohol / Genetic conditions — optional]\n\nExamination Findings at Admission\n[Vitals, systemic findings, any abnormal signs]\n\nInvestigations\n[List relevant lab results, imaging with interpretation]\nEg: CBC – WNL, Chest X-ray – consolidation in right lower lobe\n\nFinal Diagnosis (with ICD-10 Code):\n[Eg: Community-acquired pneumonia (J18.9) with Type 2 Diabetes Mellitus (E11.9)]\n\nHospital Course / Treatment Given\nSummary of key events day-wise or phase-wise (ICU, ward)\n[Antibiotics started, response noted, physiotherapy given, etc.]\n\nSurgery Performed (if any)\nName, date, brief operative note (refer to Op Summary)\n\nCondition at Discharge\n[Vitals stable / afebrile / ambulatory / tolerating orally]\n\nMedications on Discharge\nName | Dose | Frequency | Duration\nTab Amoxicillin 500 mg – 1-0-1 for 5 days\nTab Paracetamol 650 mg – SOS\n\nAdvice on Discharge\nDiet: [Normal / Diabetic / Low-salt]\nActivity: [Bed rest / Gradual mobilization]\nFollow-up: [With Dr. XYZ on DD-MM-YYYY or if symptoms worsen]\nRed flags: [Fever, bleeding, breathlessness]\n\nPrognosis / Outcome\n[Recovered / Improving / Palliative / Referred / Expired]\n\nDoctor's Name & Signature\n[Dr. Full Name]\n[Designation & Registration No.]\n", "instructions": "\nConvert notes into a comprehensive, paragraph-based discharge summary that meets Clinical Documentation Integrity (CDI) standards.\n- Organize information into clear sections\n- Expand abbreviated notes into full sentences\n- Assign an ICD-10 code to the final diagnosis\n- Ensure continuity of care instructions are detailed\n- Include all medications with complete dosing schedules\n- Highlight any follow-up requirements or warning signs\n", "sections": ["patient_details", "presenting_complaints", "history_present_illness", "past_medical_surgical_history", "allergies", "personal_family_history", "examination_findings_admission", "investigations", "provisional_final_diagnosis", "hospital_course_treatment", "surgery_performed", "condition_discharge", "medications_discharge", "advice_discharge", "prognosis_outcome", "doctor_signature"]}, "surgery": {"format": "operative summary", "style": "structured operative note", "ai_instruction": "You are a medical assistant AI. Convert the following operative notes into a structured operative summary. The report must adhere to Clinical Documentation Integrity (CDI) principles. For the 'Operative Procedure' and 'Intraoperative Findings' sections, use the detailed, granular, and structured descriptive style characteristic of SNOMED CT. For the final 'Post-operative Diagnosis', assign the appropriate ICD-10 code. IMPORTANT: Provide ONLY the final structured operative summary - do not include any instructional text, explanations, or meta-commentary.", "template_structure": "\nOperative Note:\n\nPatient Details:\nName: [Full Name or Initials]\nAge / Sex: [Years, M/F/O]\nHospital Number / IP: [XXXXXXX]\n\nDate and Time of Surgery:\n[DD-MM-YYYY, HH:MM – HH:MM]\n\nIndications for Surgery:\n[Brief history leading to surgery]\n\nPre-operative Diagnosis:\n[Diagnosis before the surgery]\n\nPost-operative Diagnosis (with ICD-10 Code):\n[Final diagnosis after the surgery, with its ICD-10 code]\n\nConsent:\n[Written informed consent obtained, including risks, benefits, alternatives]\n\nType of Anesthesia:\n[Type and any complications during induction]\n\nPositioning and Preparation:\n[Position of patient, area prepped, draping]\n\nOperative Procedure:\nIncision: [Site, type]\nExploration: [Findings - e.g., inflamed appendix, adhesions]\nSteps Taken: [Step-by-step procedure with instruments, techniques, safety checks]\nHemostasis: [Method used]\nIrrigation / Suction: [If applicable]\nClosure: [Layer-wise details - fascia, subcutaneous, skin, suture material]\nDrain Placement: [If any]\n\nIntraoperative Findings:\n[Detailed list of anatomical/pathological findings]\n\nIntraoperative Complications:\n[If any: bleeding, adhesion, anatomical variation]\n\nPost-op Plan:\n[Monitoring, antibiotics, nutrition, mobilization, labs]\n\nCondition at End of Procedure:\n[Stable / vitals normal / shifted to ICU]\n\nSpecimen Sent for HPE:\n[Yes / No; details]\n\nSignatures:\n[Operating Surgeon]\n[Assistant Surgeon]\n[Anesthetist]\n", "instructions": "\nTransform surgical notes into a detailed operative report that meets Clinical Documentation Integrity (CDI) standards.\n- Convert procedure notes into a narrative, using SNOMED CT principles for high-detail descriptions of the procedure and findings.\n- Include pre-operative, intra-operative, and post-operative details.\n- Assign an ICD-10 code to the final post-operative diagnosis.\n- Detail any complications or significant events.\n- Include post-operative care instructions.\n", "sections": ["patient_details", "date_time_surgery", "indications_surgery", "preoperative_diagnosis", "postoperative_diagnosis", "consent", "anesthesia_type", "positioning_preparation", "operative_procedure", "intraoperative_findings", "intraoperative_complications", "postop_plan", "condition_end_procedure", "specimen_hpe", "signatures"]}, "radiology": {"format": "radiology report", "style": "structured imaging interpretation", "ai_instruction": "You are a radiology assistant AI. Convert the following spoken findings and clinical details into a structured radiology report. Your response must adhere to Clinical Documentation Integrity (CDI) principles. Use SNOMED CT concepts for the 'Findings' section to ensure clinical granularity. Assign the appropriate ICD-10 code(s) to the diagnosis in the 'Impression' section. IMPORTANT: Provide ONLY the final structured output - do not include instructional text.", "template_structure": "\nRADIOLOGY REPORT\n\nPatient Details:\n  - Name: [Full Name or Initials]\n  - Age/Sex: [Years, M/F/O]\n  - Patient ID: [ID Number]\n\nExam Details:\n  - Type of Exam: [e.g., <PERSON> Scan of the Abdomen and Pelvis with IV contrast]\n  - Date of Exam: [DD-MM-YYYY]\n  - Reason for Exam: [e.g., 64-year-old female with new onset abdominal pain]\n\nComparison:\n  - [e.g., Comparison is made to prior CT scan dated DD-MM-YYYY]\n\nTechnique:\n  - [Brief description of how the scan was performed, e.g., Axial sections were obtained from the lung bases to the pubic symphysis following the administration of 100ml of non-ionic intravenous contrast.]\n\nFindings:\n  - [Detailed, organ-by-organ or system-by-system description of observations. This section should be objective and map to SNOMED CT concepts.]\n  - Lungs: [e.g., Bibasilar atelectasis is noted.]\n  - Liver: [e.g., No focal hepatic lesion identified.]\n  - Spleen: [e.g., Unremarkable.]\n  - ...etc.\n\nImpression (with ICD-10 Codes):\n  - 1. [Primary finding and diagnosis with ICD-10 code, e.g., Moderate right-sided hydronephrosis secondary to an obstructing 5 mm stone at the ureterovesical junction (N13.2, N20.1)]\n  - 2. [Secondary finding and diagnosis with ICD-10 code]\n\nRecommendations:\n  - [e.g., Suggest urology consultation. Recommend follow-up ultrasound in 6 weeks to ensure resolution.]\n\nRadiologist ID:\n  - [Dr. Name / ID / Signature token]\n", "instructions": "\nConvert dictated radiology findings into a structured report meeting CDI standards.\n- Ensure all key sections (Technique, Findings, Impression) are present.\n- Use granular, objective language in 'Findings' suitable for SNOMED CT mapping.\n- Assign specific ICD-10 codes to each diagnostic point in the 'Impression'.\n- Clearly state any recommendations for follow-up or further action.\n", "sections": ["patient_details", "exam_details", "comparison", "technique", "findings", "impression", "recommendations", "radiologist_id"]}, "dermatology": {"format": "dermatology case note", "style": "structured SOAP note", "ai_instruction": "You are a dermatology assistant AI. Convert the following consultation notes into a structured dermatology case note. Your response must adhere to CDI principles. Use precise SNOMED CT terminology for the 'Description of Lesion/Rash' in the Objective section. Assign the appropriate ICD-10 code to the diagnosis in the 'Assessment' section. IMPORTANT: Provide ONLY the final structured output - do not include instructional text.", "template_structure": "\nDERMATOLOGY CASE NOTE\n\nPatient Details:\n  - Name: [Full Name or Initials]\n  - Age/Sex: [Years, M/F/O]\n  - Date: [DD-MM-YYYY]\n\nBackground (Subjective):\n  - Chief Complaint: [e.g., New mole on back]\n  - History of Present Illness (HPI): [e.g., First noticed 3 months ago, has grown slightly, is asymptomatic, no bleeding or itching.]\n\nPast Medical History (PMH):\n  - [e.g., Personal history of dysplastic nevi. Family history of melanoma in father. No known drug allergies.]\n\nObjective/Physical Exam:\n  - Description of Lesion/Rash: [Use specific dermatological terms. e.g., On the left scapula, there is a 5mm, symmetric, uniformly brown macule with regular borders.]\n  - Procedure Note: [e.g., A 4mm punch biopsy was performed under 1% lidocaine with epinephrine. Hemostasis achieved with pressure. Specimen sent for histopathology.]\n\nAssessment (Diagnosis with ICD-10 Code):\n  - 1. [Primary diagnosis, e.g., Benign nevus (D22.5)]\n  - 2. [Differential diagnosis, e.g., Rule out dysplastic nevus (D23.5)]\n\nPlan (Treatment):\n  - [e.g., Biopsy results pending. Patient to follow up in 2 weeks for results and discussion. Advised strict sun protection and regular self-skin exams.]\n\nDoctor ID:\n  - [Dr. Name / ID / Signature token]\n", "instructions": "\nConvert a dermatology consult into a structured SOAP note meeting CDI standards.\n- Structure the note using Subjective, Objective, Assessment, and Plan sections.\n- Use specific, standard dermatological terms for lesion descriptions (for SNOMED CT).\n- Assign the correct ICD-10 code(s) to the diagnosis/assessment.\n- Detail any procedures performed and the follow-up plan.\n", "sections": ["patient_details", "background", "past_medical_history", "objective_physical_exam", "diagnosis_assessment", "treatment_plan", "doctor_id"]}, "cardiology_echo": {"format": "echocardiogram report", "style": "structured procedural report", "ai_instruction": "You are a cardiology AI assistant. Parse the following dictated findings and measurements to populate a structured echocardiogram report. Adhere to CDI principles. Ensure the 'Findings' section uses structured, standard terminology (suitable for SNOMED CT mapping). The 'Conclusion' must summarize key findings and link to the primary diagnosis with its ICD-10 code. IMPORTANT: Provide ONLY the final structured report - do not include instructional text.", "template_structure": "\nECHOCARDIOGRAM REPORT\n\nPatient Information:\n  - Name: [Full Name or Initials]\n  - Age/Sex: [Years, M/F/O]\n  - Referring Physician: [Dr. Name]\n  - Reason for Study: [e.g., Evaluation of murmur, assessment of LV function]\n\nMeasurements:\n  - Left Ventricular Ejection Fraction (LVEF): [e.g., 60-65%]\n  - LV Wall Thickness: [e.g., 1.0 cm]\n  - Aortic Root Diameter: [e.g., 3.2 cm]\n  - [Other relevant measurements]\n\nFindings:\n  - Left Ventricle: [e.g., Normal left ventricular size, wall thickness, and systolic function. No regional wall motion abnormalities.]\n  - Right Ventricle: [e.g., Normal right ventricular size and function.]\n  - Atria: [e.g., The left and right atria are normal in size.]\n  - Valves:\n    - Aortic Valve: [e.g., Trileaflet and non-calcified.]\n    - Mitral Valve: [e.g., Mild mitral regurgitation.]\n    - Tricuspid Valve: [e.g., Trivial tricuspid regurgitation.]\n    - Pulmonic Valve: [e.g., Unremarkable.]\n  - Pericardium: [e.g., No pericardial effusion.]\n\nConclusion (with ICD-10 Code):\n  - 1. [e.g., Normal left ventricular systolic function (corresponds to reason for study)]\n  - 2. [e.g., Mild mitral regurgitation (I34.0)]\n\nCardiologist ID:\n  - [Dr. Name / ID / Signature token]\n", "instructions": "\nGenerate a structured echocardiogram report from dictated notes and measurements.\n- Populate both quantitative (Measurements) and qualitative (Findings) sections accurately.\n- Use standardized terminology in the 'Findings' section (for SNOMED CT).\n- Provide a concise summary in the 'Conclusion' with a clear ICD-10 code for the primary diagnosis.\n", "sections": ["patient_information", "reason_for_study", "measurements", "findings", "conclusion_summary", "cardiologist_id"]}, "ivf_cycle": {"format": "ivf cycle summary", "style": "procedural and instructional report", "ai_instruction": "You are a reproductive medicine AI assistant. Generate a combined Embryology Lab Report and Cycle Summary from the provided data. Adhere to CDI principles. Ensure all data points are accurately captured. Include relevant ICD-10 codes for the primary diagnosis of infertility. IMPORTANT: Provide ONLY the final structured report - do not include instructional text.", "template_structure": "\nIVF CYCLE SUMMARY\n\nPatient Details:\n  - Name: [Female Partner Name] & [Male Partner Name]\n  - Female Age: [Years]\n  - Cycle Number: [#]\n  - Primary Diagnosis: [e.g., Female factor infertility due to tubal obstruction (N97.1)]\n\nProcedure Details:\n  - Procedure: [e.g., Oocyte Retrieval / Embryo Transfer]\n  - Date of Procedure: [DD-MM-YYYY]\n\nOocyte Data:\n  - Number of Follicles Aspirated: [#]\n  - Number of Oocytes Retrieved: [#]\n  - Number of Mature (MII) Oocytes: [#]\n  - Number of Oocytes Fertilized (ICSI/IVF): [#]\n\nEmbryo Development Log:\n  - Number of Embryos on Day 3: [#]\n  - Number of Embryos on Day 5 (Blastocysts): [#]\n  - Number of Embryos Cryopreserved: [#]\n\nEmbryo Transfer Note:\n  - Number of Embryos Transferred: [#]\n  - Quality/Grade of Embryos Transferred: [e.g., 1x 4AA Blastocyst, 1x 3AB Blastocyst]\n  - Ease of Procedure: [e.g., Smooth transfer, no complications.]\n\nFollow-Up Instructions:\n  - Medications: [e.g., Continue Estrace 2mg TID and Progesterone in Oil 1ml daily.]\n  - Activity: [e.g., Pelvic rest. Avoid strenuous activity for 48 hours.]\n  - Next Appointment: [e.g., Serum beta hCG test scheduled for DD-MM-YYYY.]\n\nDoctor ID:\n  - [Dr. Name / ID / Signature token]\n", "instructions": "\nCreate a detailed IVF cycle summary and lab report from clinical data.\n- Accurately capture all numerical data related to oocytes and embryos.\n- Clearly document the details of the embryo transfer.\n- Provide specific, patient-facing follow-up and medication instructions.\n- Include the primary ICD-10 code for the patient's diagnosis.\n", "sections": ["patient_details", "procedure_details", "oocyte_data", "embryo_development_log", "embryo_transfer_note", "follow_up_instructions", "doctor_id"]}, "pathology": {"format": "histopathology report", "style": "structured specimen analysis", "ai_instruction": "You are a pathology AI assistant. Convert the following gross and microscopic descriptions into a final, structured histopathology report. Adhere strictly to CDI principles. The 'Microscopic Description' must be detailed, leveraging SNOMED CT terminology for cellular and tissue features. The 'Final Diagnosis' must be definitive and include the correct ICD-10 and, if applicable, ICD-O (Oncology) codes. IMPORTANT: Provide ONLY the final structured report - do not include instructional text.", "template_structure": "\nHISTOPATHOLOGY REPORT\n\nPatient Details:\n  - Name: [Full Name or Initials]\n  - Age/Sex: [Years, M/F/O]\n  - Specimen ID: [ID Number]\n\nSpecimen Details:\n  - Specimen Source: [e.g., Skin punch biopsy, left arm]\n  - Date Received: [DD-MM-YYYY]\n  - Clinical History: [e.g., Suspected basal cell carcinoma]\n\nGross Description:\n  - [e.g., Received is one piece of tissue measuring 0.4 x 0.2 cm, labeled with the patient's name. The specimen is inked and submitted in one cassette.]\n\nMicroscopic Description:\n  - [Detailed description of findings under the microscope, suitable for SNOMED CT mapping. e.g., Sections show nests of basaloid cells with peripheral palisading, scant cytoplasm, and hyperchromatic nuclei. Nests are seen extending into the dermis. There is associated stromal retraction. The margins are clear of tumor.]\n\nFinal Diagnosis (with ICD-10 / ICD-O Codes):\n  - [e.g., Left arm, skin punch biopsy: Basal Cell Carcinoma, nodular type (ICD-10: C44.612, ICD-O: 8091/3).]\n  - [e.g., All surgical margins are negative for malignancy.]\n\nComments:\n  - [Optional notes, e.g., The tumor is present 0.2 mm from the deep margin.]\n\nPathologist ID:\n  - [Dr. Name / ID / Signature token]\n", "instructions": "\nGenerate a formal histopathology report meeting CDI standards.\n- Transcribe gross and microscopic descriptions accurately.\n- Ensure the microscopic description uses precise, standard terminology (for SNOMED CT).\n- Provide a definitive final diagnosis.\n- Assign the mandatory ICD-10 code and the relevant ICD-O code for all malignancies.\n- Note the status of surgical margins clearly.\n", "sections": ["patient_details", "specimen_details", "gross_description", "microscopic_description", "final_diagnosis", "comments", "pathologist_id"]}}