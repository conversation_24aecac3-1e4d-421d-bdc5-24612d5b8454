{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "regions": ["iad1"], "functions": {"app/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=*, microphone=*, geolocation=()"}]}, {"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}]}