import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    console.log('🔄 Next.js API Route - Received request body:', {
      primary_audio_url: body.primary_audio_url ? '✅ Present' : '❌ Missing',
      additional_audio_urls: body.additional_audio_urls?.length || 0,
      image_urls: body.image_urls?.length || 0,
      submitted_by: body.submitted_by,
      consultation_type: body.consultation_type,
      patient_name: body.patient_name
    })

    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005'
    console.log('🔄 Next.js API Route - Forwarding to:', `${apiUrl}/api/generate-summary-stream`)

    // Forward the request to the Python backend
    const response = await fetch(`${apiUrl}/api/generate-summary-stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    console.log('🔄 Next.js API Route - Backend response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('🔄 Next.js API Route - Backend error:', errorText)
      return NextResponse.json(
        { error: 'Failed to connect to backend service', details: errorText },
        { status: response.status }
      )
    }

    // Create a readable stream for the response
    const readable = new ReadableStream({
      start(controller) {
        const reader = response.body?.getReader()
        if (!reader) {
          controller.close()
          return
        }

        function pump(): Promise<void> {
          return reader!.read().then(({ done, value }) => {
            if (done) {
              controller.close()
              return
            }
            controller.enqueue(value)
            return pump()
          })
        }

        return pump()
      },
    })

    return new Response(readable, {
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    })
  } catch (error) {
    console.error('Streaming API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}