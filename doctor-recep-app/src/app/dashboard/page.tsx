import { Metadata } from 'next'
import { verifySession, getUser } from '@/lib/auth/dal'
import { getConsultations } from '@/lib/actions/consultations'
import { NewRecordingInterface } from '@/components/new/new-recording-interface'

export const metadata: Metadata = {
  title: 'Dashboard - Celer AI',
  description: 'Create new patient consultations with AI-powered summaries',
}

export default async function DashboardPage() {
  const session = await verifySession()
  
  // Parallelize dependent calls after getting session
  const [user, consultationsResult] = await Promise.all([
    getUser(),
    getConsultations(),
  ])

  const consultations = consultationsResult.success ? consultationsResult.data || [] : []

  return (
    <NewRecordingInterface 
      user={user} 
      consultations={consultations}
      doctorId={session.userId}
    />
  )
}
