import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Analytics } from "@vercel/analytics/next";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Celer AI",
  description: "AI-powered patient consultation summary system for Indian doctors",
  keywords: "doctor, patient, consultation, AI, summary, clinic, healthcare",
  authors: [{ name: "Celer AI" }],
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Doctor Reception",
  },
  // icons: {
  //   icon: "/icons/icon-192x192.png",
  //   apple: "/icons/icon-192x192.png",
  // },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#2563eb',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        {children}
        <Analytics />
      </body>
    </html>
  );
}
