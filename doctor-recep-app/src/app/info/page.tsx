import { Metadata } from 'next'
import { verifySession, getUser, getDoctorQuota } from '@/lib/auth/dal'
import { getConsultations } from '@/lib/actions/consultations'
import { DashboardHeader } from '@/components/dashboard/dashboard-header'
import { DashboardStats } from '@/components/dashboard/dashboard-stats'
import { ConsultationsList } from '@/components/dashboard/consultations-list'
import { QuotaCard } from '@/components/dashboard/quota-card'
import { ReferralStats } from '@/components/dashboard/referral-stats'
import { DashboardClient } from '@/components/dashboard/dashboard-client'

export const metadata: Metadata = {
  title: 'Info - Celer AI',
  description: 'View statistics, quota information, and referral details',
}

export default async function InfoPage() {
  const session = await verifySession()
  
  // Parallelize dependent calls after getting session
  const [user, consultationsResult, quotaInfo] = await Promise.all([
    getUser(),
    getConsultations(),
    getDoctorQuota(session.userId)
  ])

  const consultations = consultationsResult.success ? consultationsResult.data || [] : []

  // Calculate stats
  const stats = {
    total_consultations: consultations.length,
    pending_consultations: consultations.filter(c => c.status === 'pending').length,
    generated_consultations: consultations.filter(c => c.status === 'generated').length,
    approved_consultations: consultations.filter(c => c.status === 'approved').length,
    today_consultations: consultations.filter(c => {
      const today = new Date().toDateString()
      const consultationDate = new Date(c.created_at).toDateString()
      return today === consultationDate
    }).length,
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 overflow-x-hidden">
      <DashboardHeader user={user} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        <div className="space-y-6 lg:space-y-8">
          {/* Dashboard Layout: 2x2 Left, Quota Middle, Referral Right */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 2x2 Stats Grid - Left (equal width) */}
            <div className="lg:col-span-1">
              <DashboardStats stats={stats} />
            </div>
            
            {/* Quota Card - Middle (equal width) */}
            <div className="lg:col-span-1">
              {quotaInfo && <QuotaCard quota={quotaInfo} doctorId={session.userId} />}
            </div>
            
            {/* Referral Card - Right (equal width) */}
            <div className="lg:col-span-1">
              <ReferralStats doctorId={session.userId} />
            </div>
          </div>

          {/* Consultations List */}
          <div className="bg-white/80 backdrop-blur-sm shadow-lg rounded-lg border border-orange-200/50">
            <div className="px-6 py-4 border-b border-orange-200/50 bg-gradient-to-r from-orange-50/50 to-amber-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-medium text-slate-800">
                    Patient Consultations
                  </h2>
                  <p className="text-sm text-slate-600">
                    Review and manage patient consultation summaries
                  </p>
                </div>
                <a
                  href="/dashboard"
                  className="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-teal-500 to-emerald-600 hover:from-teal-600 hover:to-emerald-700 text-white rounded-full shadow-md hover:shadow-lg transition-all duration-150 transform hover:scale-105 active:scale-95"
                  title="Add New Recording"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </a>
              </div>
            </div>

            <ConsultationsList consultations={consultations} doctorName={user?.name} />
          </div>
        </div>
      </main>

      {/* Client-side components for modals */}
      <DashboardClient doctorId={session.userId} />
    </div>
  )
}