'use server'

import { createClient } from '@/lib/supabase/server'
import { ApiResponse, Json } from '@/lib/types'
import { revalidatePath } from 'next/cache'

export interface BillingPlan {
  id: string
  name: string
  description: string | null
  monthly_price: number
  quota_limit: number
  features: Json | null
  active: boolean
  created_at: string
  updated_at: string
}

export interface BillingTransaction {
  id: string
  doctor_id: string
  plan_id: string | null
  amount: number
  discount_amount: number
  final_amount: number
  payment_method: string | null
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_date: string | null
  billing_period_start: string
  billing_period_end: string
  payment_reference: string | null
  notes: string | null
  created_by: string | null
  metadata: Json | null
  created_at: string
  updated_at: string
  doctor: {
    name: string
    email: string
    clinic_name: string | null
  }
  plan: {
    name: string
    monthly_price: number
  } | null
}

export interface DoctorBillingInfo {
  id: string
  name: string
  email: string
  clinic_name: string | null
  billing_status: 'trial' | 'active' | 'suspended' | 'cancelled'
  trial_ends_at: string | null
  last_payment_date: string | null
  next_billing_date: string | null
  available_discount_amount: number
  current_plan: {
    name: string
    monthly_price: number
  } | null
  total_paid: number
  pending_payments: number
  referral_info: {
    successful_referrals: number
    discount_earned: number
    referred_by: string | null
  }
}

export async function getBillingPlans(): Promise<ApiResponse<BillingPlan[]>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('billing_plans')
      .select('*')
      .eq('active', true)
      .order('monthly_price', { ascending: true })

    if (error) {
      return { success: false, error: 'Failed to fetch billing plans' }
    }

    return { success: true, data: (data || []) as BillingPlan[] }
  } catch (error) {
    console.error('Error fetching billing plans:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getAllBillingTransactions(limit = 50, offset = 0): Promise<ApiResponse<{
  transactions: BillingTransaction[]
  total_count: number
}>> {
  try {
    const supabase = await createClient()

    // Get total count
    const { count } = await supabase
      .from('billing_transactions')
      .select('*', { count: 'exact', head: true })

    // Get transactions with related data
    const { data, error } = await supabase
      .from('billing_transactions')
      .select(`
        *,
        doctor:doctors!billing_transactions_doctor_id_fkey(name, email, clinic_name),
        plan:billing_plans!billing_transactions_plan_id_fkey(name, monthly_price)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      return { success: false, error: 'Failed to fetch billing transactions' }
    }

    return {
      success: true,
      data: {
        transactions: (data || []) as BillingTransaction[],
        total_count: count || 0
      }
    }
  } catch (error) {
    console.error('Error fetching billing transactions:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getDoctorsBillingInfo(): Promise<ApiResponse<DoctorBillingInfo[]>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('doctors')
      .select(`
        id,
        name,
        email,
        clinic_name,
        billing_status,
        trial_ends_at,
        last_payment_date,
        next_billing_date,
        available_discount_amount,
        successful_referrals,
        referral_discount_earned,
        referred_by,
        current_plan:billing_plans!doctors_current_plan_id_fkey(name, monthly_price)
      `)
      .eq('approved', true)
      .order('created_at', { ascending: false })

    if (error) {
      return { success: false, error: 'Failed to fetch doctors billing info' }
    }

    // Get payment summaries for each doctor
    const doctorsWithBilling = await Promise.all(
      (data || []).map(async (doctor) => {
        // Get total paid and pending amounts
        const { data: paymentSummary } = await supabase
          .from('billing_transactions')
          .select('payment_status, final_amount')
          .eq('doctor_id', doctor.id)

        const totalPaid = paymentSummary
          ?.filter(p => p.payment_status === 'paid')
          .reduce((sum, p) => sum + p.final_amount, 0) || 0

        const pendingPayments = paymentSummary
          ?.filter(p => p.payment_status === 'pending')
          .reduce((sum, p) => sum + p.final_amount, 0) || 0

        // Get referrer info separately if exists
        let referrerName = null
        if (doctor.referred_by) {
          const { data: referrer } = await supabase
            .from('doctors')
            .select('name')
            .eq('id', doctor.referred_by)
            .single()
          referrerName = referrer?.name || null
        }

        return {
          id: doctor.id,
          name: doctor.name,
          email: doctor.email,
          clinic_name: doctor.clinic_name,
          billing_status: doctor.billing_status,
          trial_ends_at: doctor.trial_ends_at,
          last_payment_date: doctor.last_payment_date,
          next_billing_date: doctor.next_billing_date,
          available_discount_amount: doctor.available_discount_amount || 0,
          current_plan: Array.isArray(doctor.current_plan) ? doctor.current_plan[0] : doctor.current_plan,
          total_paid: totalPaid,
          pending_payments: pendingPayments,
          referral_info: {
            successful_referrals: doctor.successful_referrals || 0,
            discount_earned: doctor.referral_discount_earned || 0,
            referred_by: referrerName
          }
        }
      })
    )

    return { success: true, data: doctorsWithBilling as DoctorBillingInfo[] }
  } catch (error) {
    console.error('Error fetching doctors billing info:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function createBillingTransaction(
  doctorId: string,
  planId: string,
  amount: number,
  billingPeriodDays = 31,
  notes?: string
): Promise<ApiResponse<string>> {
  try {
    const supabase = await createClient()

    const billingPeriodStart = new Date()
    const billingPeriodEnd = new Date(billingPeriodStart)
    billingPeriodEnd.setDate(billingPeriodEnd.getDate() + billingPeriodDays)

    const { data, error } = await supabase
      .from('billing_transactions')
      .insert({
        doctor_id: doctorId,
        plan_id: planId,
        amount: amount,
        final_amount: amount,
        billing_period_start: billingPeriodStart.toISOString(),
        billing_period_end: billingPeriodEnd.toISOString(),
        notes: notes || null
      })
      .select('id')
      .single()

    if (error) {
      return { success: false, error: 'Failed to create billing transaction' }
    }

    revalidatePath('/admin/dashboard')
    return { success: true, data: data.id }
  } catch (error) {
    console.error('Error creating billing transaction:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function markPaymentPaid(
  transactionId: string,
  paymentMethod?: string,
  paymentReference?: string
): Promise<ApiResponse<boolean>> {
  try {
    console.log('Marking payment as paid:', { transactionId, paymentMethod, paymentReference })
    
    const supabase = await createClient()

    // First verify the transaction exists and is pending
    const { data: transaction, error: fetchError } = await supabase
      .from('billing_transactions')
      .select('id, payment_status, doctor_id')
      .eq('id', transactionId)
      .single()

    if (fetchError) {
      console.error('Error fetching transaction:', fetchError)
      return { success: false, error: `Transaction not found: ${fetchError.message}` }
    }

    if (!transaction) {
      return { success: false, error: 'Transaction not found' }
    }

    if (transaction.payment_status !== 'pending') {
      return { success: false, error: `Cannot mark payment as paid. Current status: ${transaction.payment_status}` }
    }

    // Use complete_payment function to handle referral logic
    const { error: rpcError } = await supabase.rpc('complete_payment', {
      transaction_id: transactionId
    })

    if (rpcError) {
      console.error('Database error completing payment:', rpcError)
      return { success: false, error: `Failed to complete payment: ${rpcError.message}` }
    }

    // Update payment method and reference if provided
    if (paymentMethod || paymentReference) {
      const { error: updateError } = await supabase
        .from('billing_transactions')
        .update({
          payment_method: paymentMethod || null,
          payment_reference: paymentReference || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', transactionId)

      if (updateError) {
        console.error('Error updating payment details:', updateError)
        // Don't fail the whole operation for this
      }
    }

    console.log('Payment completed successfully with referral handling')
    
    revalidatePath('/admin/dashboard')
    
    return { success: true, data: true }
  } catch (error) {
    console.error('Unexpected error marking payment as paid:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    return { success: false, error: `Unexpected error: ${errorMessage}` }
  }
}

export async function applyReferralDiscount(
  transactionId: string,
  discountAmount: number
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase.rpc('apply_referral_discount', {
      transaction_id: transactionId,
      discount_amount: discountAmount
    })

    if (error) {
      return { success: false, error: 'Failed to apply referral discount' }
    }

    revalidatePath('/admin/dashboard')
    return { success: true, data: data || false }
  } catch (error) {
    console.error('Error applying referral discount:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function updateDoctorBillingStatus(
  doctorId: string,
  status: 'trial' | 'active' | 'suspended' | 'cancelled'
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('doctors')
      .update({ billing_status: status })
      .eq('id', doctorId)

    if (error) {
      return { success: false, error: 'Failed to update billing status' }
    }

    revalidatePath('/admin/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Error updating billing status:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getBillingStats(): Promise<ApiResponse<{
  total_revenue: number
  monthly_revenue: number
  pending_payments: number
  active_subscriptions: number
  trial_users: number
  referral_discounts_given: number
}>> {
  try {
    const supabase = await createClient()

    // Get revenue stats
    const { data: revenueData } = await supabase
      .from('billing_transactions')
      .select('final_amount, payment_status, payment_date')
      .eq('payment_status', 'paid')

    const totalRevenue = revenueData?.reduce((sum, t) => sum + t.final_amount, 0) || 0
    
    const currentMonth = new Date()
    currentMonth.setDate(1)
    const monthlyRevenue = revenueData
      ?.filter(t => t.payment_date && new Date(t.payment_date) >= currentMonth)
      .reduce((sum, t) => sum + t.final_amount, 0) || 0

    // Get pending payments
    const { data: pendingData } = await supabase
      .from('billing_transactions')
      .select('final_amount')
      .eq('payment_status', 'pending')

    const pendingPayments = pendingData?.reduce((sum, t) => sum + t.final_amount, 0) || 0

    // Get user stats
    const { count: activeSubscriptions } = await supabase
      .from('doctors')
      .select('*', { count: 'exact', head: true })
      .eq('billing_status', 'active')

    const { count: trialUsers } = await supabase
      .from('doctors')
      .select('*', { count: 'exact', head: true })
      .eq('billing_status', 'trial')

    // Get referral discount stats
    const { data: discountData } = await supabase
      .from('referral_discounts')
      .select('discount_amount')
      .eq('status', 'applied')

    const referralDiscountsGiven = discountData?.reduce((sum, d) => sum + d.discount_amount, 0) || 0

    return {
      success: true,
      data: {
        total_revenue: totalRevenue,
        monthly_revenue: monthlyRevenue,
        pending_payments: pendingPayments,
        active_subscriptions: activeSubscriptions || 0,
        trial_users: trialUsers || 0,
        referral_discounts_given: referralDiscountsGiven
      }
    }
  } catch (error) {
    console.error('Error fetching billing stats:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}