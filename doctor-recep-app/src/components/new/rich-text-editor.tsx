'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { useEffect } from 'react'

interface RichTextEditorProps {
  content: string
  onChange: (content: string) => void
  isEditing: boolean
  isDarkMode: boolean
}

export function RichTextEditor({ content, onChange, isEditing, isDarkMode }: RichTextEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: 'Consultation summary will appear here...',
      }),
    ],
    content,
    editable: isEditing,
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: `prose max-w-none focus:outline-none ${
          isDarkMode
            ? 'prose-invert text-gray-200'
            : 'text-amber-800'
        } ${
          isEditing
            ? isDarkMode
              ? 'bg-black border border-gray-700 rounded-lg p-4'
              : 'bg-amber-50/50 border border-orange-200 rounded-lg p-4'
            : isDarkMode
              ? 'bg-black border border-gray-700 rounded-lg p-4'
              : 'bg-amber-50/50 border border-orange-200 rounded-lg p-4'
        }`,
      },
    },
  })

  useEffect(() => {
    if (editor && editor.getHTML() !== content) {
      editor.commands.setContent(content)
    }
  }, [content, editor])

  useEffect(() => {
    if (editor) {
      editor.setEditable(isEditing)
    }
  }, [isEditing, editor])

  if (!editor) {
    return null
  }

  return (
    <div className={`min-h-[200px] ${
      isEditing
        ? ''
        : isDarkMode
          ? 'text-gray-200'
          : 'text-amber-800'
    }`}>
      <EditorContent editor={editor} />
    </div>
  )
}
