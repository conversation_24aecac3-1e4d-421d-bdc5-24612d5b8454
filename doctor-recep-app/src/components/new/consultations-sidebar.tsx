'use client'

import { useState, useEffect } from 'react'
import { Search, Plus, X, Clock, CheckCircle, FileText, Calendar, ChevronDown } from 'lucide-react'
import { Consultation } from '@/lib/types'
// import { formatDate, formatRelativeTime } from '@/lib/utils'
import { motion } from 'framer-motion'

interface ConsultationsSidebarProps {
  consultations: Consultation[]
  onConsultationSelect: (consultation: Consultation) => void
  selectedConsultation: Consultation | null
  isDarkMode: boolean
  doctorId: string
  isMobile?: boolean
  onClose?: () => void
}

// Format date to readable string (SSR-safe)
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)

  // Use a consistent format that works on both server and client
  const year = date.getFullYear()
  const month = date.toLocaleDateString('en-US', { month: 'short' })
  const day = date.getDate().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const ampm = date.getHours() >= 12 ? 'PM' : 'AM'
  const displayHours = date.getHours() % 12 || 12

  return `${month} ${day}, ${year} at ${displayHours.toString().padStart(2, '0')}:${minutes} ${ampm}`
}

// Format relative time (e.g., "2 hours ago")
const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  } else {
    return formatDate(dateString)
  }
}

export function ConsultationsSidebar({
  consultations,
  onConsultationSelect,
  selectedConsultation,
  isDarkMode,
  doctorId: _doctorId,
  isMobile = false,
  onClose
}: ConsultationsSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [isClient, setIsClient] = useState(false)
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'generated' | 'approved'>('all')
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'yesterday' | 'custom'>('all')
  const [customDate, setCustomDate] = useState<string>('')
  const [templateFilter, setTemplateFilter] = useState<'all' | 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'>('all')
  const [showStatusDropdown, setShowStatusDropdown] = useState(false)
  const [showDateDropdown, setShowDateDropdown] = useState(false)
  const [showTemplateDropdown, setShowTemplateDropdown] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Filter consultations based on search and filters
  const getDateFilteredConsultations = (consultations: Consultation[]) => {
    if (dateFilter === 'all') return consultations

    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    return consultations.filter(consultation => {
      const consultationDate = new Date(consultation.created_at)

      switch (dateFilter) {
        case 'today':
          return consultationDate.toDateString() === today.toDateString()
        case 'yesterday':
          return consultationDate.toDateString() === yesterday.toDateString()
        case 'custom':
          if (!customDate) return true
          const selectedDate = new Date(customDate)
          return consultationDate.toDateString() === selectedDate.toDateString()
        default:
          return true
      }
    })
  }

  const filteredConsultations = consultations.filter(consultation => {
    // Search filter
    const searchLower = searchQuery.toLowerCase()
    const matchesSearch = !searchQuery || (
      consultation.patient_number?.toString().includes(searchLower) ||
      consultation.patient_name?.toLowerCase().includes(searchLower) ||
      consultation.doctor_notes?.toLowerCase().includes(searchLower) ||
      consultation.ai_generated_note?.toLowerCase().includes(searchLower)
    )

    // Status filter
    const matchesStatus = statusFilter === 'all' || consultation.status === statusFilter

    // Template filter
    const matchesTemplate = templateFilter === 'all' || consultation.consultation_type === templateFilter

    return matchesSearch && matchesStatus && matchesTemplate
  })

  const dateAndFilteredConsultations = getDateFilteredConsultations(filteredConsultations)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-orange-500" />
      case 'generated':
        return <FileText className="w-4 h-4 text-blue-500" />
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full"
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-orange-200 text-orange-900 dark:bg-orange-900/30 dark:text-orange-400`
      case 'generated':
        return `${baseClasses} bg-blue-200 text-blue-900 dark:bg-blue-900/30 dark:text-blue-400`
      case 'approved':
        return `${baseClasses} bg-green-200 text-green-900 dark:bg-green-900/30 dark:text-green-400`
      default:
        return `${baseClasses} bg-gray-200 text-gray-900 dark:bg-black dark:text-gray-400`
    }
  }

  const getTypeBadge = (type: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full"
    switch (type) {
      case 'outpatient':
        return `${baseClasses} bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400`
      case 'discharge':
        return `${baseClasses} bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400`
      case 'surgery':
        return `${baseClasses} bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400`
      case 'radiology':
        return `${baseClasses} bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400`
      case 'dermatology':
        return `${baseClasses} bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400`
      case 'cardiology_echo':
        return `${baseClasses} bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400`
      case 'ivf_cycle':
        return `${baseClasses} bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400`
      case 'pathology':
        return `${baseClasses} bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-black dark:text-gray-400`
    }
  }

  return (
    <div className={`h-full flex flex-col transition-colors duration-300 ${
      isDarkMode
        ? 'bg-black'
        : 'bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50'
    }`}>
      {/* Header */}
      <div className="p-6 transition-colors duration-300">
        <div className="flex items-center justify-between mb-4">
          <h2 className={`text-lg font-semibold ${
            isDarkMode ? 'text-gray-100' : 'text-amber-800'
          }`}>
            Consultations
          </h2>
          {isMobile && onClose && (
            <button
              onClick={onClose}
              className={`p-1 rounded-lg transition-colors ${
                isDarkMode
                  ? 'hover:bg-gray-800 text-gray-300'
                  : 'hover:bg-orange-100/60 text-amber-700'
              }`}
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Search Bar */}
        <div className="relative mb-4">
          <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-500'
          }`} />
          <input
            type="text"
            placeholder="Search by patient name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border transition-colors ${
              isDarkMode
                ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-teal-500'
                : 'bg-white/80 border-orange-200 text-amber-800 placeholder-amber-600/60 focus:border-teal-500'
            } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex space-x-2 mb-4">
          {/* Status Filter */}
          <div className="relative">
            <button
              onClick={() => {
                setShowStatusDropdown(!showStatusDropdown)
                setShowDateDropdown(false)
                setShowTemplateDropdown(false)
              }}
              className={`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-300 hover:bg-gray-800'
                  : 'bg-white/80 border-orange-200 text-amber-700 hover:bg-orange-50'
              }`}
            >
              <span>{statusFilter === 'all' ? 'All' : statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}</span>
              <ChevronDown className="w-3 h-3" />
            </button>
            {showStatusDropdown && (
              <div className={`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${
                isDarkMode
                  ? 'bg-black border-gray-700'
                  : 'bg-white border-orange-200'
              }`}>
                {['all', 'pending', 'generated', 'approved'].map((status) => (
                  <button
                    key={status}
                    onClick={() => {
                      setStatusFilter(status as 'all' | 'pending' | 'generated' | 'approved')
                      setShowStatusDropdown(false)
                    }}
                    className={`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${
                      statusFilter === status
                        ? isDarkMode
                          ? 'bg-teal-900/30 text-teal-400'
                          : 'bg-teal-50 text-teal-700'
                        : isDarkMode
                          ? 'text-gray-300 hover:bg-gray-800'
                          : 'text-amber-700 hover:bg-orange-50'
                    }`}
                  >
                    {status === 'all' ? 'All' : status.charAt(0).toUpperCase() + status.slice(1)}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Date Filter */}
          <div className="relative">
            <button
              onClick={() => {
                setShowDateDropdown(!showDateDropdown)
                setShowStatusDropdown(false)
                setShowTemplateDropdown(false)
              }}
              className={`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-300 hover:bg-gray-800'
                  : 'bg-white/80 border-orange-200 text-amber-700 hover:bg-orange-50'
              }`}
            >
              <Calendar className="w-3 h-3" />
              <span>{dateFilter === 'all' ? 'All' : dateFilter.charAt(0).toUpperCase() + dateFilter.slice(1)}</span>
              <ChevronDown className="w-3 h-3" />
            </button>
            {showDateDropdown && (
              <div className={`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${
                isDarkMode
                  ? 'bg-black border-gray-700'
                  : 'bg-white border-orange-200'
              }`}>
                {['all', 'today', 'yesterday', 'custom'].map((date) => (
                  <button
                    key={date}
                    onClick={() => {
                      setDateFilter(date as 'all' | 'today' | 'yesterday' | 'custom')
                      setShowDateDropdown(false)
                      if (date !== 'custom') {
                        setCustomDate('')
                      }
                    }}
                    className={`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${
                      dateFilter === date
                        ? isDarkMode
                          ? 'bg-teal-900/30 text-teal-400'
                          : 'bg-teal-50 text-teal-700'
                        : isDarkMode
                          ? 'text-gray-300 hover:bg-gray-800'
                          : 'text-amber-700 hover:bg-orange-50'
                    }`}
                  >
                    {date === 'all' ? 'All' : date.charAt(0).toUpperCase() + date.slice(1)}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Template Filter */}
          <div className="relative">
            <button
              onClick={() => {
                setShowTemplateDropdown(!showTemplateDropdown)
                setShowStatusDropdown(false)
                setShowDateDropdown(false)
              }}
              className={`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-300 hover:bg-gray-800'
                  : 'bg-white/80 border-orange-200 text-amber-700 hover:bg-orange-50'
              }`}
            >
              <FileText className="w-3 h-3" />
              <span>{templateFilter === 'all' ? 'All' : templateFilter.charAt(0).toUpperCase() + templateFilter.slice(1)}</span>
              <ChevronDown className="w-3 h-3" />
            </button>
            {showTemplateDropdown && (
              <div className={`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${
                isDarkMode
                  ? 'bg-black border-gray-700'
                  : 'bg-white border-orange-200'
              }`}>
                {['all', 'outpatient', 'discharge', 'surgery', 'radiology', 'dermatology', 'cardiology_echo', 'ivf_cycle', 'pathology'].map((template) => (
                  <button
                    key={template}
                    onClick={() => {
                      setTemplateFilter(template as 'all' | 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology')
                      setShowTemplateDropdown(false)
                    }}
                    className={`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${
                      templateFilter === template
                        ? isDarkMode
                          ? 'bg-teal-900/30 text-teal-400'
                          : 'bg-teal-50 text-teal-700'
                        : isDarkMode
                          ? 'text-gray-300 hover:bg-gray-800'
                          : 'text-amber-700 hover:bg-orange-50'
                    }`}
                  >
                    {template === 'all' ? 'All' :
                     template === 'cardiology_echo' ? 'Cardiology Echo' :
                     template === 'ivf_cycle' ? 'IVF Cycle' :
                     template.charAt(0).toUpperCase() + template.slice(1)}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Custom Date Input */}
        {dateFilter === 'custom' && (
          <div className="mb-4">
            <input
              type="date"
              value={customDate}
              onChange={(e) => setCustomDate(e.target.value)}
              className={`w-full px-3 py-2 text-xs rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-100 focus:border-teal-500'
                  : 'bg-white/80 border-orange-200 text-amber-800 focus:border-teal-500'
              } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
            />
          </div>
        )}

        {/* Add Recording Button */}
        <button
          onClick={() => onConsultationSelect(null as unknown as Consultation)} // Clear selection to start new
          className={`w-full flex items-center justify-center space-x-2 py-3 rounded-lg font-medium transition-colors ${
            isDarkMode
              ? 'bg-teal-600 hover:bg-teal-700 text-white'
              : 'bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white shadow-md'
          }`}
        >
          <Plus className="w-5 h-5" />
          <span>Add Consulation</span>
        </button>
      </div>

      {/* Consultations List */}
      <div className="flex-1 overflow-y-auto">
        {dateAndFilteredConsultations.length === 0 ? (
          <div className={`p-4 text-center ${
            isDarkMode ? 'text-gray-300' : 'text-amber-600'
          }`}>
            <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              {searchQuery ? 'No recordings found' : 'No recordings yet'}
            </p>
          </div>
        ) : (
          <div className="p-2">
            {dateAndFilteredConsultations.map((consultation) => (
              <motion.div
                key={consultation.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onConsultationSelect(consultation)}
                className={`p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 ${
                  selectedConsultation?.id === consultation.id
                    ? isDarkMode
                      ? 'bg-teal-900/30 border border-teal-600'
                      : 'bg-gradient-to-r from-teal-50 to-emerald-50 border border-teal-200 shadow-sm'
                    : isDarkMode
                      ? 'bg-black hover:bg-gray-800 border border-gray-700'
                      : 'bg-white/60 hover:bg-white/80 border border-orange-200/60 hover:border-orange-300'
                }`}
              >
                {/* Patient Info */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-medium text-sm truncate ${
                      isDarkMode ? 'text-gray-100' : 'text-amber-800'
                    }`}>
                      {consultation.patient_name || `Patient #${consultation.patient_number || 'N/A'}`}
                    </h3>
                    <div className="flex items-center space-x-1 mt-1">
                      <Calendar className={`w-3 h-3 ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-500'
                      }`} />
                      <span className={`text-xs ${
                        isDarkMode ? 'text-gray-300' : 'text-amber-600'
                      }`}>
                        {isClient ? formatRelativeTime(consultation.created_at) : formatDate(consultation.created_at)}
                      </span>
                    </div>
                  </div>
                  {getStatusIcon(consultation.status)}
                </div>

                {/* Badges */}
                <div className="flex flex-wrap gap-1 mb-2">
                  <span className={getStatusBadge(consultation.status)}>
                    {consultation.status}
                  </span>
                  <span className={getTypeBadge(consultation.consultation_type || 'outpatient')}>
                    {consultation.consultation_type || 'outpatient'}
                  </span>
                </div>

                {/* Preview Text */}
                {(consultation.doctor_notes || consultation.ai_generated_note) && (
                  <p className={`text-xs line-clamp-2 ${
                    isDarkMode ? 'text-gray-300' : 'text-amber-700'
                  }`}>
                    {consultation.doctor_notes || 
                     consultation.ai_generated_note?.substring(0, 100) + '...'}
                  </p>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
