'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, Consultation } from '@/lib/types'
import { NewNavbar } from './new-navbar'
import { ConsultationsSidebar } from './consultations-sidebar'
import { RecordingMainArea } from './recording-main-area'
import { motion, AnimatePresence } from 'framer-motion'

interface NewRecordingInterfaceProps {
  user: Doctor | null
  consultations: Consultation[]
  doctorId: string
}

export function NewRecordingInterface({ user, consultations, doctorId }: NewRecordingInterfaceProps) {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null)
  const [isMobile, setIsMobile] = useState(false)

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 640)
      // Auto-close sidebar on mobile when screen size changes
      if (window.innerWidth > 640) {
        setIsSidebarOpen(false)
      }
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Check for dark mode preference - default to light mode
  useEffect(() => {
    const darkModePreference = localStorage.getItem('darkMode')
    if (darkModePreference) {
      setIsDarkMode(darkModePreference === 'true')
    } else {
      // Default to light mode (beige theme)
      setIsDarkMode(false)
    }
  }, [])

  // Apply dark mode to document
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
    localStorage.setItem('darkMode', isDarkMode.toString())
  }, [isDarkMode])

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
  }

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  const handleConsultationSelect = (consultation: Consultation) => {
    setSelectedConsultation(consultation)
    // Close sidebar on mobile after selection
    if (isMobile) {
      setIsSidebarOpen(false)
    }
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode
        ? 'bg-black text-gray-100'
        : 'bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 text-gray-900'
    }`}>
      {/* Navbar */}
      <NewNavbar
        user={user}
        isDarkMode={isDarkMode}
        onToggleDarkMode={toggleDarkMode}
        onToggleSidebar={toggleSidebar}
        isMobile={isMobile}
      />

      <div className="flex h-[calc(100vh-64px)]">
        {/* Desktop Sidebar */}
        {!isMobile && (
          <div className="w-80">
            <ConsultationsSidebar
              consultations={consultations}
              onConsultationSelect={handleConsultationSelect}
              selectedConsultation={selectedConsultation}
              isDarkMode={isDarkMode}
              doctorId={doctorId}
            />
          </div>
        )}

        {/* Mobile Sidebar Overlay */}
        <AnimatePresence>
          {isMobile && isSidebarOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 z-40"
                onClick={() => setIsSidebarOpen(false)}
              />
              
              {/* Sidebar */}
              <motion.div
                initial={{ x: '-100%' }}
                animate={{ x: 0 }}
                exit={{ x: '-100%' }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                className={`fixed left-0 top-20 bottom-0 w-[75%] z-50 transition-colors duration-300 ${
                  isDarkMode
                    ? 'bg-gray-950'
                    : 'bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50'
                }`}
              >
                <ConsultationsSidebar
                  consultations={consultations}
                  onConsultationSelect={handleConsultationSelect}
                  selectedConsultation={selectedConsultation}
                  isDarkMode={isDarkMode}
                  doctorId={doctorId}
                  isMobile={true}
                  onClose={() => setIsSidebarOpen(false)}
                />
              </motion.div>
            </>
          )}
        </AnimatePresence>

        {/* Main Recording Area */}
        <div className="flex-1 overflow-hidden">
          <RecordingMainArea
            selectedConsultation={selectedConsultation}
            isDarkMode={isDarkMode}
            doctorId={doctorId}
            doctorName={user?.name || undefined}
            onConsultationUpdate={(updated) => {
              setSelectedConsultation(updated)
              // You might want to refresh consultations list here
            }}
          />
        </div>
      </div>
    </div>
  )
}
