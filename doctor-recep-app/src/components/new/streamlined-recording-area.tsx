'use client'

import { useState, useRef, useEffect } from 'react'
import { Mic, Square, Play, Pause, Upload, Wand2, Edit3, Copy, X, ChevronDown, ChevronUp, Check, Loader2, Trash2, Save } from 'lucide-react'
import { Consultation } from '@/lib/types'
// import { formatDuration } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import { RichTextEditor } from './rich-text-editor'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { createConsultationWithFiles, approveConsultation, addAdditionalAudio, addAdditionalImages, updateConsultationType, saveEditedNote, clearEditedNote, deleteAdditionalAudio, deleteConsultationImage } from '@/lib/actions/consultations'

import Image from 'next/image'

// Convert basic markdown to HTML for RichTextEditor
function convertMarkdownToHtml(markdown: string): string {
  return markdown
    // Convert **bold** to <strong>bold</strong>
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert *italic* to <em>italic</em>
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Convert line breaks to <br> tags
    .replace(/\n/g, '<br>')
    // Convert double line breaks to paragraphs
    .replace(/<br><br>/g, '</p><p>')
    // Split by paragraph breaks and wrap each section in <p> tags
    .split('</p><p>')
    .map(section => section.trim())
    .filter(section => section.length > 0)
    .map(section => `<p>${section}</p>`)
    .join('')
    // If no paragraphs were created, wrap the whole content in a single paragraph
    || `<p>${markdown.replace(/\n/g, '<br>')}</p>`
}

// Convert HTML back to markdown for editing
function convertHtmlToMarkdown(html: string): string {
  return html
    // Convert <strong> to **bold**
    .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
    // Convert <em> to *italic*
    .replace(/<em>(.*?)<\/em>/g, '*$1*')
    // Convert <br> tags to line breaks
    .replace(/<br\s*\/?>/g, '\n')
    // Convert paragraphs to double line breaks
    .replace(/<\/p><p>/g, '\n\n')
    // Remove paragraph tags
    .replace(/<\/?p>/g, '')
    // Clean up any remaining HTML tags
    .replace(/<[^>]*>/g, '')
    // Decode HTML entities
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
}

interface StreamlinedRecordingAreaProps {
  selectedConsultation: Consultation | null
  isDarkMode: boolean
  doctorId: string
  doctorName?: string
  onConsultationUpdate: (consultation: Consultation) => void
  // State props
  patientName: string
  setPatientName: (name: string) => void
  selectedTemplate: string
  setSelectedTemplate: (template: string) => void
  isRecording: boolean
  isPaused: boolean
  recordingDuration: number
  audioBlob: Blob | null
  audioFile: File | null
  images: Array<{ id: string; file: File; preview: string }>
  setImages: (images: Array<{ id: string; file: File; preview: string }>) => void
  isGenerating: boolean
  setIsGenerating: (generating: boolean) => void
  summary: string
  setSummary: (summary: string) => void
  isEditing: boolean
  setIsEditing: (editing: boolean) => void
  additionalNotes: string
  setAdditionalNotes: (notes: string) => void
  // Function props
  startRecording: () => void
  pauseRecording: () => void
  stopRecording: () => void
  handleImageUpload: (files: FileList) => void
  removeImage: (id: string) => void
  clearAudio: () => void
}

const templates = [
  { id: 'outpatient', name: 'Outpatient Consultation', color: 'teal' },
  { id: 'discharge', name: 'Discharge Summary', color: 'purple' },
  { id: 'surgery', name: 'Operative Note', color: 'red' },
  { id: 'radiology', name: 'Radiology Report', color: 'blue' },
  { id: 'dermatology', name: 'Dermatology Note', color: 'green' },
  { id: 'cardiology_echo', name: 'Echocardiogram Report', color: 'pink' },
  { id: 'ivf_cycle', name: 'IVF Cycle Summary', color: 'orange' },
  { id: 'pathology', name: 'Histopathology Report', color: 'indigo' }
]

// Format duration in seconds to MM:SS format
const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

export function StreamlinedRecordingArea(props: StreamlinedRecordingAreaProps) {
  const {
    selectedConsultation, isDarkMode, patientName, setPatientName,
    selectedTemplate, setSelectedTemplate, isRecording, isPaused, recordingDuration,
    audioBlob, images, isGenerating, setIsGenerating, summary, setSummary,
    isEditing, setIsEditing, additionalNotes, setAdditionalNotes,
    startRecording, pauseRecording, stopRecording, handleImageUpload, removeImage,
    clearAudio, _doctorId, doctorName
  } = props

  const [isTemplateOpen, setIsTemplateOpen] = useState(false)
  const [isNotesOpen, setIsNotesOpen] = useState(false)
  const [audioPlaying, setAudioPlaying] = useState<string | null>(null)
  const [copySuccess, setCopySuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isApproving, setIsApproving] = useState(false)
  const [streamingText, setStreamingText] = useState('')
  const [submitMessage, setSubmitMessage] = useState('')
  const [editedMarkdown, setEditedMarkdown] = useState('')
  const [isSavingEdit, setIsSavingEdit] = useState(false)
  const [messageTimeoutRef, setMessageTimeoutRef] = useState<NodeJS.Timeout | null>(null)

  // Delete functionality state
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null)
  const [showDeleteMenu, setShowDeleteMenu] = useState<{ type: 'audio' | 'image', url: string } | null>(null)

  // Additional upload states for generated consultations (unused but kept for future features)
  const [_additionalImages, _setAdditionalImages] = useState<Array<{ id: string, url: string, preview?: string }>>([])
  const [_isRecordingAdditional, _setIsRecordingAdditional] = useState(false)
  const [_additionalAudioBlob, _setAdditionalAudioBlob] = useState<Blob | null>(null)
  const [_mediaRecorder, _setMediaRecorder] = useState<MediaRecorder | null>(null)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const audioRefs = useRef<{ [key: string]: HTMLAudioElement }>({})

  // Clear messages when consultation changes
  useEffect(() => {
    setSubmitMessage('')
    if (messageTimeoutRef) {
      clearTimeout(messageTimeoutRef)
      setMessageTimeoutRef(null)
    }
  }, [selectedConsultation?.id, messageTimeoutRef])

  // Cleanup long press timer on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer) {
        clearTimeout(longPressTimer)
      }
    }
  }, [longPressTimer])

  // Function to set message with auto-clear timeout
  const setSubmitMessageWithTimeout = (message: string, timeout = 3000) => {
    // Clear any existing timeout
    if (messageTimeoutRef) {
      clearTimeout(messageTimeoutRef)
    }

    setSubmitMessage(message)

    // Set new timeout to clear message
    if (message) {
      const timeoutId = setTimeout(() => {
        setSubmitMessage('')
        setMessageTimeoutRef(null)
      }, timeout)
      setMessageTimeoutRef(timeoutId)
    }
  }

  const selectedTemplateData = templates.find(t => t.id === selectedTemplate) || templates[0]
  const isApproved = selectedConsultation?.status === 'approved'
  const canEdit = !isApproved
  const hasContent = summary || selectedConsultation?.ai_generated_note || selectedConsultation?.edited_note

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(summary)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  // Handle template change with immediate database sync
  const handleTemplateChange = async (newTemplate: string) => {
    setSelectedTemplate(newTemplate)

    // If we have a selected consultation, update it in the database immediately
    if (selectedConsultation) {
      try {
        const result = await updateConsultationType(
          selectedConsultation.id,
          newTemplate as 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'
        )

        if (result.success) {
          // Update the consultation object to reflect the change
          const updatedConsultation = {
            ...selectedConsultation,
            consultation_type: newTemplate
          }
          props.onConsultationUpdate(updatedConsultation)
        } else {
          console.error('Failed to update consultation type:', result.error)
          setSubmitMessageWithTimeout(`Failed to update template: ${result.error}`)
        }
      } catch (error) {
        console.error('Error updating consultation type:', error)
        setSubmitMessageWithTimeout('Failed to update template')
      }
    }
  }

  // Handle editing - convert HTML to markdown for editing
  const handleStartEdit = () => {
    const currentContent = summary || selectedConsultation?.edited_note || selectedConsultation?.ai_generated_note || ''
    // If content is HTML (from rich text editor), convert to markdown
    const markdownContent = currentContent.includes('<') ? convertHtmlToMarkdown(currentContent) : currentContent
    setEditedMarkdown(markdownContent)
    setIsEditing(true)
  }

  // Handle saving edited content
  const handleSaveEdit = async () => {
    if (!selectedConsultation || !editedMarkdown.trim()) {
      setSubmitMessage('No content to save')
      return
    }

    setIsSavingEdit(true)
    setSubmitMessage('')

    try {
      const result = await saveEditedNote(selectedConsultation.id, editedMarkdown)

      if (result.success) {
        setSubmitMessageWithTimeout('Changes saved successfully!')
        // Update the summary display with the edited content (convert to HTML for display)
        setSummary(convertMarkdownToHtml(editedMarkdown))
        setIsEditing(false)

        // Update the consultation object to reflect the change
        const updatedConsultation = {
          ...selectedConsultation,
          edited_note: editedMarkdown
        }
        props.onConsultationUpdate(updatedConsultation)
      } else {
        setSubmitMessageWithTimeout(result.error || 'Failed to save changes')
      }
    } catch (error) {
      console.error('Error saving edited note:', error)
      setSubmitMessageWithTimeout('Failed to save changes')
    } finally {
      setIsSavingEdit(false)
    }
  }

  // Handle cancel editing
  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditedMarkdown('')
  }

  // Delete handlers
  const handleDeleteAudio = async (audioUrl: string) => {
    if (!selectedConsultation) return

    try {
      setSubmitMessage('Deleting audio...')
      const result = await deleteAdditionalAudio(selectedConsultation.id, audioUrl)

      if (result.success) {
        setSubmitMessageWithTimeout('Audio deleted successfully!')

        // Refresh consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.find(c => c.id === selectedConsultation.id)
          if (updatedConsultation) {
            props.onConsultationUpdate(updatedConsultation)
          }
        }
      } else {
        setSubmitMessageWithTimeout(result.error || 'Failed to delete audio')
      }
    } catch (_error) {
      setSubmitMessageWithTimeout('Failed to delete audio')
    } finally {
      setShowDeleteMenu(null)
    }
  }

  const handleDeleteImage = async (imageUrl: string) => {
    if (!selectedConsultation) return

    try {
      setSubmitMessage('Deleting image...')
      const result = await deleteConsultationImage(selectedConsultation.id, imageUrl)

      if (result.success) {
        setSubmitMessageWithTimeout('Image deleted successfully!')

        // Refresh consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.find(c => c.id === selectedConsultation.id)
          if (updatedConsultation) {
            props.onConsultationUpdate(updatedConsultation)
          }
        }
      } else {
        setSubmitMessageWithTimeout(result.error || 'Failed to delete image')
      }
    } catch (_error) {
      setSubmitMessageWithTimeout('Failed to delete image')
    } finally {
      setShowDeleteMenu(null)
    }
  }

  // Long press handlers for mobile
  const handleLongPressStart = (type: 'audio' | 'image', url: string) => {
    const timer = setTimeout(() => {
      setShowDeleteMenu({ type, url })
    }, 500) // 500ms long press
    setLongPressTimer(timer)
  }

  const handleLongPressEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      setLongPressTimer(null)
    }
  }

  const handleSubmitConsultation = async () => {
    // Check if we're working with an existing consultation or creating a new one
    const isExistingConsultation = !!selectedConsultation



    // Validation logic differs for existing vs new consultations
    if (isExistingConsultation) {
      // For existing consultations: we need either new audio, new images, or additional notes
      if (!audioBlob && images.length === 0 && !additionalNotes.trim()) {
        setSubmitMessage('Please add new audio, images, or additional notes to update the consultation')
        return
      }
    } else {
      // For new consultations: we need both audio and patient name
      if (!audioBlob || !patientName.trim()) {
        setSubmitMessage('Please provide patient name and record audio')
        return
      }
    }

    setIsSubmitting(true)
    setSubmitMessage('')

    try {
      if (isExistingConsultation) {
        // Handle new images
        if (images.length > 0) {
          const imageFiles = images.map(img => img.file)

          const imageResult = await addAdditionalImages(selectedConsultation.id, imageFiles)
          if (!imageResult.success) {
            setSubmitMessage(imageResult.error || 'Failed to add additional images')
            return
          }

        }

        // Handle new audio
        if (audioBlob) {
          const audioFile = new File([audioBlob], `additional_audio_${Date.now()}.webm`, {
            type: 'audio/webm'
          })

          const audioResult = await addAdditionalAudio(selectedConsultation.id, audioFile)
          if (!audioResult.success) {
            setSubmitMessage(audioResult.error || 'Failed to add additional audio')
            return
          }

        }

        // Handle additional notes update
        if (additionalNotes.trim() !== (selectedConsultation.additional_notes || '').trim()) {
          const { updateAdditionalNotes } = await import('@/lib/actions/consultations')
          const notesResult = await updateAdditionalNotes(selectedConsultation.id, additionalNotes.trim())
          if (!notesResult.success) {
            setSubmitMessage(notesResult.error || 'Failed to update additional notes')
            return
          }
        }

        setSubmitMessageWithTimeout('Consultation updated successfully!')

        // FIX: Refetch the consultation from database to get the latest state
        try {
          const { getConsultations } = await import('@/lib/actions/consultations')
          const consultationsResult = await getConsultations()

          if (consultationsResult.success && consultationsResult.data) {
            // Find the updated consultation in the fresh data
            const updatedConsultation = consultationsResult.data.find(c => c.id === selectedConsultation.id)

            if (updatedConsultation) {
              // Call onConsultationUpdate with the fresh data from database
              props.onConsultationUpdate(updatedConsultation)
              return { success: true, data: updatedConsultation }
            }
          }

          // Fallback if we can't find the updated consultation
          props.onConsultationUpdate(selectedConsultation)
          return { success: true, data: selectedConsultation }

        } catch (fetchError) {
          console.error('Failed to refetch consultations:', fetchError)
          // Fallback to original consultation if fetch fails
          props.onConsultationUpdate(selectedConsultation)
          return { success: true, data: selectedConsultation }
        }

      } else {
        // Convert blob to File
        if (!audioBlob) {
          setSubmitMessage('No audio recording available')
          return
        }
        const audioFile = new File([audioBlob], `recording_${Date.now()}.webm`, {
          type: 'audio/webm'
        })

        // Extract File objects from images
        const imageFiles = images.map(img => img.file)

        const result = await createConsultationWithFiles(
          audioFile,
          imageFiles,
          [], // No additional audio files
          'doctor',
          additionalNotes || undefined,
          selectedTemplate as "outpatient" | "discharge" | "surgery" | "radiology" | "dermatology" | "cardiology_echo" | "ivf_cycle" | "pathology",
          patientName || undefined
        )

        if (result.success && result.data) {
          setSubmitMessageWithTimeout(`Consultation submitted! ${result.data.patient_name}`)
          props.onConsultationUpdate(result.data)
          return result
        } else {
          setSubmitMessage('error' in result ? result.error : 'Failed to submit consultation')
          return result
        }
      }
    } catch (error) {
      console.error('❌ Save error:', error)
      setSubmitMessage(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`)
      return { success: false, error: 'Network error' }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGenerate = async (consultationToUse?: Consultation) => {
    // For existing consultations, use selectedConsultation directly (same as update function)
    // For new consultations, use the provided consultation from save result
    if (selectedConsultation) {
      // Existing consultation - use selectedConsultation directly like update function
      console.log('🎯 Using existing consultation:', selectedConsultation.id)
    } else if (consultationToUse) {
      // New consultation - use the provided consultation
      console.log('🎯 Using new consultation:', consultationToUse.id)
    } else if (audioBlob) {
      // No consultation but have audio - save first then generate
      const result = await handleSubmitConsultation()
      if (result && result.success && result.data) {
        return handleGenerate(result.data)
      }
      return
    } else {
      setSubmitMessage('Please record audio or select a consultation first')
      return
    }

    // Use the same pattern as update function - selectedConsultation for existing, consultationToUse for new
    const consultationId = selectedConsultation?.id || consultationToUse?.id
    // FIX: Use selectedTemplate from UI state instead of stored consultation_type
    const consultationType = selectedTemplate // This ensures the current UI selection is used
    const doctorNotes = selectedConsultation?.doctor_notes || consultationToUse?.doctor_notes

    if (!consultationId) {
      setSubmitMessage('No consultation ID available')
      return
    }

    setIsGenerating(true)
    setStreamingText('')
    setSummary('')

    // Clear edited note when regenerating (for existing consultations)
    if (selectedConsultation) {
      try {
        await clearEditedNote(selectedConsultation.id)
      } catch (error) {
        console.error('Failed to clear edited note:', error)
        // Continue with generation even if clearing fails
      }
    }

    try {
      console.log('🎯 Starting generation with consultation ID:', consultationId)
      console.log('🎯 Using template type:', consultationType)

      // Get the consultation data to extract file URLs (same as dashboard approach)
      const currentConsultation = selectedConsultation || consultationToUse
      if (!currentConsultation) {
        setSubmitMessage('No consultation data available')
        setIsGenerating(false)
        return
      }

      // Use direct API call like dashboard (this supports streaming properly)
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          primary_audio_url: currentConsultation.primary_audio_url,
          additional_audio_urls: Array.isArray(currentConsultation.additional_audio_urls) ? currentConsultation.additional_audio_urls : [],
          image_urls: Array.isArray(currentConsultation.image_urls) ? currentConsultation.image_urls : [],
          submitted_by: currentConsultation.submitted_by || 'doctor',
          consultation_type: consultationType || 'outpatient',
          doctor_notes: doctorNotes || undefined,
          additional_notes: additionalNotes || undefined,
          patient_name: currentConsultation.patient_name || undefined,
          doctor_name: doctorName || undefined,
          created_at: currentConsultation.created_at || undefined,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No reader available')
      }

      const decoder = new TextDecoder()
      let fullSummary = ''
      setStreamingText('')

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'chunk' && data.text) {
                // Format text with proper line breaks
                const formattedText = data.text.replace(/\\n/g, '\n').replace(/\n\n+/g, '\n\n')
                fullSummary += formattedText
                // Instantly update streaming text with accumulated content
                setStreamingText(fullSummary)
              }
            } catch (_e) {
              // Ignore parse errors
            }
          }
        }
      }

      // Convert markdown to HTML for the RichTextEditor
      const htmlSummary = convertMarkdownToHtml(fullSummary)
      setSummary(htmlSummary)
      setStreamingText('')
      setIsGenerating(false)
      setSubmitMessageWithTimeout('Summary generated successfully!')

      // FIX: Clear editing state since we have new AI-generated content
      setIsEditing(false)
      setEditedMarkdown('')

      // Save the streamed summary using the proper server action
      try {
        const { saveStreamingSummary } = await import('@/lib/actions/consultations')
        const saveResult = await saveStreamingSummary(consultationId, fullSummary)

        if (saveResult.success) {
          // FIX: Update consultation with new AI content and cleared edited_note
          if (props.onConsultationUpdate) {
            props.onConsultationUpdate({
              ...currentConsultation,
              ai_generated_note: fullSummary,
              edited_note: null, // Important: clear this to reflect database state
              status: 'generated'
            })
          }
        } else {
          console.error('Failed to save streaming summary:', saveResult.error)
          setSubmitMessage(`Summary generated but failed to save: ${saveResult.error}`)
        }
      } catch (saveError) {
        console.error('Error saving streaming summary:', saveError)
        setSubmitMessage('Summary generated but failed to save. Please try regenerate.')
      }

    } catch (error) {
      console.error('❌ Generate error:', error)
      setSubmitMessage(`Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setIsGenerating(false)
    }
  }

  const handleApprove = async () => {
    if (!selectedConsultation || !summary.trim()) {
      setSubmitMessage('Please generate a summary before approving')
      return
    }

    setIsApproving(true)
    setSubmitMessage('')

    try {
      const result = await approveConsultation(selectedConsultation.id, summary)

      if (result.success) {
        setSubmitMessageWithTimeout('Consultation approved successfully!')
        // Update consultation status
        if (props.onConsultationUpdate) {
          props.onConsultationUpdate({
            ...selectedConsultation,
            status: 'approved',
            edited_note: summary
          })
        }
      } else {
        setSubmitMessage(result.error || 'Failed to approve consultation')
      }
    } catch (_error) {
      setSubmitMessage('Failed to approve consultation')
    } finally {
      setIsApproving(false)
    }
  }

  const toggleAudioPlayback = (audioId: string) => {
    const audio = audioRefs.current[audioId]
    if (!audio) return

    if (audioPlaying === audioId) {
      audio.pause()
      setAudioPlaying(null)
    } else {
      // Pause other audios
      Object.values(audioRefs.current).forEach(a => a.pause())
      audio.play()
      setAudioPlaying(audioId)
    }
  }

  return (
    <div className="space-y-8 max-w-5xl">
      {/* Mobile: Patient Name, Template, Approve Button */}
      <div className="flex flex-col sm:hidden gap-4 pl-3">
        {/* Patient Name */}
        <div>
          <label className={`block text-lg font-semibold mb-4 ${
            isDarkMode ? 'text-gray-200' : 'text-amber-700'
          }`}>
            Patient Name
          </label>
          <Input
            value={patientName}
            onChange={(e) => setPatientName(e.target.value)}
            placeholder="Enter patient name"
            disabled={!!selectedConsultation}
            className={`h-10 px-4 py-2 rounded-lg border transition-colors ${
              isDarkMode
                ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-teal-500'
                : 'bg-white/80 border-orange-200 text-amber-800 placeholder-amber-600/60 focus:border-teal-500'
            } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
          />
        </div>

        {/* Template and Approve Button on same row */}
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <label className={`block text-lg font-semibold mb-4 ${
              isDarkMode ? 'text-gray-200' : 'text-amber-700'
            }`}>
              Template
            </label>
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => canEdit && setIsTemplateOpen(!isTemplateOpen)}
                disabled={!canEdit}
                className={`w-full justify-between h-10 px-4 py-2 rounded-lg border transition-colors ${
                  isDarkMode
                    ? 'bg-black border-gray-700 text-gray-100 hover:bg-gray-800 focus:border-teal-500'
                    : 'bg-white/80 border-orange-200 text-amber-800 hover:bg-white/90 focus:border-teal-500'
                } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
              >
                <span>{selectedTemplateData.name}</span>
                {canEdit && <ChevronDown className="h-4 w-4" />}
              </Button>

              <AnimatePresence>
                {isTemplateOpen && canEdit && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className={`absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-20 ${
                      isDarkMode
                        ? 'bg-gray-950 border-gray-700'
                        : 'bg-gradient-to-br from-amber-50 to-orange-50 border-orange-200'
                    }`}
                  >
                    {templates.map((template) => (
                      <button
                        key={template.id}
                        onClick={() => {
                          handleTemplateChange(template.id)
                          setIsTemplateOpen(false)
                        }}
                        className={`w-full px-3 py-2 text-left transition-colors ${
                          selectedTemplate === template.id
                            ? isDarkMode
                              ? 'bg-teal-900/30 text-teal-400'
                              : 'bg-teal-50 text-teal-700'
                            : isDarkMode
                              ? 'hover:bg-gray-800 text-gray-200'
                              : 'hover:bg-orange-50 text-amber-800'
                        }`}
                      >
                        {template.name}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Mobile Approve Button - Same level as template */}
          {hasContent && selectedConsultation && !isApproved && (
            <Button
              onClick={handleApprove}
              disabled={isApproving || !summary.trim()}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-md transition-colors !h-10 !px-6 [&]:!px-6"
              style={{ height: '40px', paddingLeft: '24px !important', paddingRight: '24px !important', minWidth: '120px', width: '120px' }}
            >
              {isApproving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Approve
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Desktop: Patient Name, Template, Approve Button */}
      <div className="hidden sm:block pl-3">
        <div className="flex gap-4 items-end">
        {/* Patient Name */}
        <div className="flex-1 max-w-xs">
          <label className={`block text-lg font-semibold mb-4 ${
            isDarkMode ? 'text-gray-200' : 'text-amber-700'
          }`}>
            Patient Name
          </label>
          <Input
            value={patientName}
            onChange={(e) => setPatientName(e.target.value)}
            placeholder="Enter patient name"
            disabled={!!selectedConsultation}
            className={`h-10 px-4 py-2 rounded-lg border transition-colors ${
              isDarkMode
                ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-teal-500'
                : 'bg-white/80 border-orange-200 text-amber-800 placeholder-amber-600/60 focus:border-teal-500'
            } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
          />
        </div>

        {/* Template */}
        <div className="flex-1 max-w-xs">
          <label className={`block text-lg font-semibold mb-4 ${
            isDarkMode ? 'text-gray-200' : 'text-amber-700'
          }`}>
            Template
          </label>
          <div className="relative">
            <Button
              variant="outline"
              onClick={() => canEdit && setIsTemplateOpen(!isTemplateOpen)}
              disabled={!canEdit}
              className={`w-full justify-between h-10 px-4 py-2 rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-100 hover:bg-gray-800 focus:border-teal-500'
                  : 'bg-white/80 border-orange-200 text-amber-800 hover:bg-white/90 focus:border-teal-500'
              } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
            >
              <span>{selectedTemplateData.name}</span>
              {canEdit && <ChevronDown className="h-4 w-4" />}
            </Button>

            <AnimatePresence>
              {isTemplateOpen && canEdit && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className={`absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-20 ${
                    isDarkMode
                      ? 'bg-gray-950 border-gray-700'
                      : 'bg-gradient-to-br from-amber-50 to-orange-50 border-orange-200'
                  }`}
                >
                  {templates.map((template) => (
                    <button
                      key={template.id}
                      onClick={() => {
                        handleTemplateChange(template.id)
                        setIsTemplateOpen(false)
                      }}
                      className={`w-full px-3 py-2 text-left transition-colors ${
                        selectedTemplate === template.id
                          ? isDarkMode
                            ? 'bg-teal-900/30 text-teal-400'
                            : 'bg-teal-50 text-teal-700'
                          : isDarkMode
                            ? 'hover:bg-gray-800 text-gray-200'
                            : 'hover:bg-orange-50 text-amber-800'
                      }`}
                    >
                      {template.name}
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Desktop Approve Button - Positioned to align with regenerate button */}
        {hasContent && selectedConsultation && !isApproved && (
          <Button
            onClick={handleApprove}
            disabled={isApproving || !summary.trim()}
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-md transition-colors !h-10 !px-6 [&]:!px-6 ml-8"
            style={{ height: '40px', paddingLeft: '24px !important', paddingRight: '24px !important', minWidth: '120px', width: '120px' }}
          >
            {isApproving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Approving...
              </>
            ) : (
              <>
                <Check className="w-4 h-4 mr-2" />
                Approve
              </>
            )}
          </Button>
        )}
        </div>
      </div>

      {/* Recording Controls */}
      <div className="pl-3">
        {/* Mobile: Recording controls with Save button positioned to the right */}
        <div className="flex sm:hidden items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Recording Controls */}
            {!isRecording ? (
              <Button
                onClick={startRecording}
                disabled={isApproved}
                size="sm"
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md rounded-full w-10 h-10 p-0"
              >
                <Mic className="w-4 h-4" />
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  onClick={pauseRecording}
                  size="sm"
                  variant="outline"
                  className="border-yellow-500 text-yellow-600 hover:bg-yellow-50 rounded-full w-10 h-10 p-0"
                >
                  {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                </Button>
                <Button
                  onClick={stopRecording}
                  size="sm"
                  variant="outline"
                  className="border-red-500 text-red-600 hover:bg-red-50 rounded-full w-10 h-10 p-0"
                >
                  <Square className="w-4 h-4" />
                </Button>
                {/* Mobile Recording Timer */}
                <div className="flex items-center gap-1 ml-2">
                  <Badge variant="secondary" className="font-mono text-sm px-2 py-1">
                    {formatDuration(recordingDuration)}
                  </Badge>
                  {isPaused && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-500 text-xs">
                      Paused
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Upload Button */}
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Chrome-specific fix: Use setTimeout to ensure click happens after event handling
                setTimeout(() => {
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }, 0);
              }}
              disabled={isApproved}
              variant="outline"
              size="sm"
              className={`rounded-full transition-colors ${
                isDarkMode
                  ? 'border-teal-600 text-teal-400 hover:bg-teal-900/20 hover:border-teal-500'
                  : 'border-orange-300 text-amber-700 hover:bg-orange-50'
              }`}
            >
              <Upload className="w-4 h-4" />
            </Button>
          </div>

          {/* Mobile Save Button - Positioned to the right */}
          <Button
            onClick={handleSubmitConsultation}
            disabled={
              isSubmitting ||
              (selectedConsultation
                ? (!audioBlob && images.length === 0 && !additionalNotes.trim()) // For existing: need new audio OR images OR additional notes
                : (!audioBlob || !patientName.trim()) // For new: need both audio AND patient name
              )
            }
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-md transition-colors !h-10 !px-6 [&]:!px-6"
            style={{ height: '40px', paddingLeft: '24px !important', paddingRight: '24px !important', minWidth: '120px', width: '120px' }}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {selectedConsultation ? 'Update' : 'Save'}
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                {selectedConsultation ? 'Update' : 'Save'}
              </>
            )}
          </Button>
        </div>

        {/* Desktop: Multi-column layout */}
        <div className="hidden sm:block">
          <div className="flex gap-4 items-center">
          {/* Left side - Recording Controls + Upload Button */}
          <div className="flex items-center gap-3 flex-1 max-w-xs">
            {/* Recording Controls */}
            {!isRecording ? (
              <Button
                onClick={startRecording}
                disabled={isApproved}
                size="sm"
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md rounded-full w-10 h-10 p-0"
              >
                <Mic className="w-4 h-4" />
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  onClick={pauseRecording}
                  size="sm"
                  variant="outline"
                  className="border-yellow-500 text-yellow-600 hover:bg-yellow-50 rounded-full w-10 h-10 p-0"
                >
                  {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                </Button>
                <Button
                  onClick={stopRecording}
                  size="sm"
                  variant="outline"
                  className="border-red-500 text-red-600 hover:bg-red-50 rounded-full w-10 h-10 p-0"
                >
                  <Square className="w-4 h-4" />
                </Button>
                <div className="flex items-center gap-2 ml-3">
                  <Badge variant="secondary" className="font-mono text-lg px-3 py-1">
                    {formatDuration(recordingDuration)}
                  </Badge>
                  {isPaused && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-500">
                      Paused
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Upload Button */}
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Chrome-specific fix: Use setTimeout to ensure click happens after event handling
                setTimeout(() => {
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }, 0);
              }}
              disabled={isApproved}
              variant="outline"
              size="sm"
              className={`rounded-full transition-colors ${
                isDarkMode
                  ? 'border-teal-600 text-teal-400 hover:bg-teal-900/20 hover:border-teal-500'
                  : 'border-orange-300 text-amber-700 hover:bg-orange-50'
              }`}
            >
              <Upload className="w-4 h-4 mr-2" />
              <span>Images</span>
            </Button>
          </div>

          {/* Desktop: Update Button - Below template dropdown */}
          <div className="flex-1 max-w-xs">
            <Button
              onClick={handleSubmitConsultation}
              disabled={
                isSubmitting ||
                (selectedConsultation
                  ? (!audioBlob && images.length === 0 && !additionalNotes.trim()) // For existing: need new audio OR images OR additional notes
                  : (!audioBlob || !patientName.trim()) // For new: need both audio AND patient name
                )
              }
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-md transition-colors !h-10 !px-6 [&]:!px-6"
              style={{ height: '40px', paddingLeft: '24px !important', paddingRight: '24px !important', minWidth: '120px', width: '120px' }}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {selectedConsultation ? 'Update' : 'Save'}
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {selectedConsultation ? 'Update' : 'Save'}
                </>
              )}
            </Button>
          </div>

          {/* Desktop: Generate Button - Below approve button */}
          <Button
            onClick={() => handleGenerate()}
            disabled={isGenerating || (!audioBlob && !selectedConsultation)}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md transition-colors !h-10 !px-6 [&]:!px-6 ml-8"
            style={{ height: '40px', paddingLeft: '24px !important', paddingRight: '24px !important', minWidth: '120px', width: '120px' }}
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4 mr-2" />
                {(selectedConsultation && (selectedConsultation.ai_generated_note || selectedConsultation.edited_note)) ? 'Regenerate' : 'Generate'}
              </>
            )}
          </Button>
          </div>
        </div>
      </div>

      {/* Status Message */}
        {submitMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`mt-4 p-3 rounded-md ${
              isDarkMode
                ? 'bg-blue-950/50 border border-blue-800'
                : 'bg-blue-50 border border-blue-200'
            }`}
          >
            <p className={`text-sm ${
              isDarkMode ? 'text-blue-200' : 'text-blue-800'
            }`}>{submitMessage}</p>
          </motion.div>
        )}

      {/* Audio Recordings */}
      {(audioBlob || selectedConsultation?.primary_audio_url || (selectedConsultation?.additional_audio_urls && Array.isArray(selectedConsultation.additional_audio_urls) && selectedConsultation.additional_audio_urls.filter((url): url is string => typeof url === 'string' && url.trim() !== '').length > 0)) && (
        <div className="flex flex-wrap gap-3 pl-0">
          {audioBlob && (
            <div className={`flex items-center gap-2 rounded-lg px-3 py-2 ${
              isDarkMode ? 'bg-black' : 'bg-transparent'
            }`}>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => toggleAudioPlayback('new')}
                className="!h-10 !w-10 p-0 rounded-full bg-blue-600 hover:bg-blue-700 text-white"
                style={{ height: '40px', width: '40px' }}
              >
                {audioPlaying === 'new' ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  setAudioPlaying(null)
                  clearAudio()
                }}
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
              <audio
                ref={(el) => { if (el) audioRefs.current['new'] = el }}
                src={URL.createObjectURL(audioBlob)}
                onEnded={() => setAudioPlaying(null)}
              />
            </div>
          )}

          {selectedConsultation?.primary_audio_url && (
            <div className={`flex items-center gap-2 rounded-lg px-3 py-2 ${
              isDarkMode ? 'bg-black' : 'bg-transparent'
            }`}>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => toggleAudioPlayback('primary')}
                className="!h-10 !w-10 p-0 rounded-full bg-orange-600 hover:bg-orange-700 text-white"
                style={{ height: '40px', width: '40px' }}
              >
                {audioPlaying === 'primary' ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
              <audio
                ref={(el) => { if (el) audioRefs.current['primary'] = el }}
                src={selectedConsultation.primary_audio_url}
                onEnded={() => setAudioPlaying(null)}
              />
            </div>
          )}

          {/* Additional Audio Files */}
          {selectedConsultation?.additional_audio_urls &&
           Array.isArray(selectedConsultation.additional_audio_urls) &&
           selectedConsultation.additional_audio_urls
             .filter((url): url is string => typeof url === 'string' && url.trim() !== '')
             .map((url, index) => (
               <div
                 key={`additional-${index}`}
                 className={`flex items-center gap-2 rounded-lg px-3 py-2 relative group ${
                   isDarkMode ? 'bg-black' : 'bg-transparent'
                 }`}
                 onMouseEnter={() => selectedConsultation?.status !== 'approved' && setShowDeleteMenu({ type: 'audio', url })}
                 onMouseLeave={() => setShowDeleteMenu(null)}
                 onTouchStart={() => selectedConsultation?.status !== 'approved' && handleLongPressStart('audio', url)}
                 onTouchEnd={handleLongPressEnd}
                 onTouchCancel={handleLongPressEnd}
               >
                 <Button
                   size="sm"
                   variant="ghost"
                   onClick={() => toggleAudioPlayback(`additional-${index}`)}
                   className="!h-10 !w-10 p-0 rounded-full bg-teal-600 hover:bg-teal-700 text-white"
                   style={{ height: '40px', width: '40px' }}
                 >
                   {audioPlaying === `additional-${index}` ? (
                     <Pause className="w-4 h-4" />
                   ) : (
                     <Play className="w-4 h-4" />
                   )}
                 </Button>

                 {/* Delete button - Desktop hover / Mobile long press */}
                 {selectedConsultation?.status !== 'approved' && showDeleteMenu?.type === 'audio' && showDeleteMenu?.url === url && (
                   <Button
                     size="sm"
                     onClick={() => handleDeleteAudio(url)}
                     className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white border-2 border-white shadow-lg z-10 transition-all duration-200"
                     title="Delete audio"
                   >
                     <Trash2 className="w-3 h-3" />
                   </Button>
                 )}

                 <audio
                   ref={(el) => { if (el) audioRefs.current[`additional-${index}`] = el }}
                   src={url}
                   onEnded={() => setAudioPlaying(null)}
                 />
               </div>
             ))}
        </div>
      )}

      {/* Images Preview */}
      {(images.length > 0 || (selectedConsultation?.image_urls && Array.isArray(selectedConsultation.image_urls) && selectedConsultation.image_urls.length > 0)) && (
        <div className={`p-4 rounded-lg transition-colors ${
          isDarkMode
            ? 'bg-black'
            : 'bg-transparent'
        }`}>
          <div className="flex flex-wrap gap-3">
            {images.map((image) => (
              <div key={image.id} className="relative group">
                <div className={`w-20 h-20 rounded-lg overflow-hidden border-2 ${
                  isDarkMode
                    ? 'bg-black border-gray-700'
                    : 'bg-transparent border-orange-200'
                }`}>
                  <Image
                    src={image.preview}
                    alt="Uploaded"
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </div>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => removeImage(image.id)}
                  className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            ))}

            {Array.isArray(selectedConsultation?.image_urls) &&
             selectedConsultation.image_urls
               .filter((url): url is string => typeof url === 'string' && url.trim() !== '')
               .map((url, index) => (
              <div
                key={`existing-${index}`}
                className={`w-20 h-20 rounded-lg overflow-hidden border-2 relative group ${
                  isDarkMode
                    ? 'bg-black border-gray-700'
                    : 'bg-transparent border-orange-200'
                }`}
                onMouseEnter={() => selectedConsultation?.status !== 'approved' && setShowDeleteMenu({ type: 'image', url })}
                onMouseLeave={() => setShowDeleteMenu(null)}
                onTouchStart={() => selectedConsultation?.status !== 'approved' && handleLongPressStart('image', url)}
                onTouchEnd={handleLongPressEnd}
                onTouchCancel={handleLongPressEnd}
              >
                <Image
                  src={url}
                  alt={`Image ${index + 1}`}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />

                {/* Delete button - Desktop hover / Mobile long press */}
                {selectedConsultation?.status !== 'approved' && showDeleteMenu?.type === 'image' && showDeleteMenu?.url === url && (
                  <Button
                    size="sm"
                    onClick={() => handleDeleteImage(url)}
                    className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white border-2 border-white shadow-lg z-10 transition-all duration-200"
                    title="Delete image"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Additional Notes Dropdown with Mobile Regenerate Button */}
      <div className="w-full">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => setIsNotesOpen(!isNotesOpen)}
            className={`flex items-center gap-2 transition-colors ${
              isDarkMode
                ? 'text-gray-300 hover:text-gray-200'
                : 'text-amber-600 hover:text-amber-700'
            }`}
          >
            <span>Additional Notes</span>
            {isNotesOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>

          {/* Mobile Regenerate Button - Same level as Additional Notes, right side */}
          <div className="sm:hidden">
            <Button
              onClick={() => handleGenerate()}
              disabled={isGenerating || (!audioBlob && !selectedConsultation)}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md transition-colors !h-10 !px-6 [&]:!px-6"
              style={{ height: '40px', paddingLeft: '24px !important', paddingRight: '24px !important', minWidth: '120px', width: '120px' }}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4 mr-2" />
                  {(selectedConsultation && (selectedConsultation.ai_generated_note || selectedConsultation.edited_note)) ? 'Regenerate' : 'Generate'}
                </>
              )}
            </Button>
          </div>
        </div>

        <AnimatePresence>
          {isNotesOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3"
            >
              <div className={`p-4 pr-4 sm:pr-20 rounded-lg transition-colors ${
                isDarkMode
                  ? 'bg-black'
                  : 'bg-transparent'
              }`}>
                <textarea
                  value={additionalNotes}
                  onChange={(e) => setAdditionalNotes(e.target.value)}
                  placeholder="Add any additional notes or observations..."
                  rows={6}
                  className={`w-full px-3 py-2 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-colors ${
                    isDarkMode
                      ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400'
                      : 'bg-amber-50/50 border-orange-200 text-amber-800 placeholder-amber-600/60'
                  }`}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Summary Section - Mobile friendly with extended width */}
      {(summary || streamingText || isGenerating || hasContent) && (
        <div className={`py-6 pr-4 sm:pr-20 pl-2 rounded-lg transition-colors ${
          isDarkMode
            ? 'bg-black'
            : 'bg-transparent'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold ${
              isDarkMode ? 'text-gray-100' : 'text-amber-800'
            }`}>Consultation Summary</h3>
            <div className="flex items-center gap-2">
              {(summary || hasContent) && (
                <>
                  {!isEditing ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleStartEdit}
                      className={`transition-colors ${
                        isDarkMode
                          ? 'text-gray-300 hover:text-gray-200'
                          : 'text-amber-600 hover:text-amber-700'
                      }`}
                    >
                      <Edit3 className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleSaveEdit}
                        disabled={isSavingEdit}
                        className={`transition-colors ${
                          isDarkMode
                            ? 'text-green-300 hover:text-green-200'
                            : 'text-green-600 hover:text-green-700'
                        }`}
                      >
                        {isSavingEdit ? (
                          <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                        ) : (
                          <Save className="w-4 h-4 mr-1" />
                        )}
                        Save
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCancelEdit}
                        className={`transition-colors ${
                          isDarkMode
                            ? 'text-gray-300 hover:text-gray-200'
                            : 'text-amber-600 hover:text-amber-700'
                        }`}
                      >
                        <X className="w-4 h-4 mr-1" />
                        Cancel
                      </Button>
                    </div>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopy}
                    className={`transition-colors ${
                      isDarkMode
                        ? 'text-gray-300 hover:text-gray-200'
                        : 'text-amber-600 hover:text-amber-700'
                    }`}
                  >
                    {copySuccess ? (
                      <>
                        <Check className="w-4 h-4 mr-1 text-green-500" />
                        Copied
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-1" />
                        Copy
                      </>
                    )}
                  </Button>
                </>
              )}
            </div>
          </div>

          {isGenerating && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-3" />
                <p className={`text-sm ${
                  isDarkMode ? 'text-gray-300' : 'text-amber-600'
                }`}>Generating AI summary...</p>
              </div>
            </div>
          )}

          {streamingText && (
            <div className={`rounded-lg p-4 border-l-4 border-blue-500 ${
              isDarkMode ? 'bg-black' : 'bg-blue-50'
            }`}>
              <div className="prose max-w-none">
                <div className={`whitespace-pre-wrap ${
                  isDarkMode ? 'text-gray-200' : 'text-gray-700'
                }`}>
                  {streamingText}
                  <span className="inline-block w-2 h-5 bg-blue-500 animate-pulse ml-1"></span>
                </div>
              </div>
            </div>
          )}

          {(summary || hasContent) && !isGenerating && (
            <>
              {isEditing ? (
                // Show markdown editor when editing
                <div className={`rounded-lg border ${
                  isDarkMode
                    ? 'bg-black border-gray-700'
                    : 'bg-amber-50/50 border-orange-200'
                }`}>
                  <textarea
                    value={editedMarkdown}
                    onChange={(e) => setEditedMarkdown(e.target.value)}
                    placeholder="Edit your consultation summary in markdown..."
                    rows={15}
                    className={`w-full px-4 py-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-teal-500 transition-colors font-mono text-sm ${
                      isDarkMode
                        ? 'bg-black text-gray-100 placeholder-gray-400'
                        : 'bg-amber-50/50 text-amber-800 placeholder-amber-600/60'
                    }`}
                  />
                </div>
              ) : (
                // Show rich text display when not editing
                <RichTextEditor
                  content={convertMarkdownToHtml(
                    summary ||
                    selectedConsultation?.edited_note ||
                    selectedConsultation?.ai_generated_note ||
                    ''
                  )}
                  onChange={setSummary}
                  isEditing={false}
                  isDarkMode={isDarkMode}
                />
              )}
            </>
          )}
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
        className="hidden"
      />
    </div>
  )
}
