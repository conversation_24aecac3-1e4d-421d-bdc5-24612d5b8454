'use client'

import { useState } from 'react'
import { Save, RotateCcw, Lock } from 'lucide-react'
import { PasswordChangeModal } from './password-change-modal'
interface SettingsFormProps {
  doctorId: string
  doctorName: string
  doctorEmail: string
}

export function SettingsForm({ doctorId, doctorName, doctorEmail }: SettingsFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState<'success' | 'error'>('success')
  const [showPasswordModal, setShowPasswordModal] = useState(false)

  const handleSave = async () => {
    setIsLoading(true)
    setMessage('')

    try {
      // Since we removed template config settings, just show success message
      setMessageType('success')
      setMessage('Settings updated successfully!')
    } catch {
      setMessageType('error')
      setMessage('An unexpected error occurred')
    } finally {
      setIsLoading(false)
      setTimeout(() => setMessage(''), 3000)
    }
  }

  const handleReset = () => {
    // No template config to reset anymore
    setMessage('')
  }

  return (
    <div className="space-y-8">
      {/* Doctor Info & Security */}
      <div className="bg-orange-50 rounded-lg p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
          <h3 className="text-lg font-medium text-slate-800 mb-2 sm:mb-0">Doctor Information</h3>
          <button
            onClick={() => setShowPasswordModal(true)}
            className="flex items-center space-x-2 px-4 py-2 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 transition-all duration-150 transform hover:scale-105 active:scale-95 w-fit"
          >
            <Lock className="w-4 h-4" />
            <span>Change Password</span>
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-slate-600">Name:</span>
            <span className="ml-2 text-slate-800">{doctorName}</span>
          </div>
          <div>
            <span className="font-medium text-slate-600">Email:</span>
            <span className="ml-2 text-slate-800">{doctorEmail}</span>
          </div>
        </div>
      </div>



      {/* Status Message */}
      {message && (
        <div className={`p-4 rounded-md ${
          messageType === 'success'
            ? 'bg-green-50 border border-green-200'
            : 'bg-red-50 border border-red-200'
        }`}>
          <p className={`text-sm ${
            messageType === 'success' ? 'text-green-700' : 'text-red-700'
          }`}>
            {message}
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t border-orange-200">
        <button
          onClick={handleReset}
          className="flex items-center space-x-2 px-4 py-2 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 transition-all duration-150 transform hover:scale-105 active:scale-95"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Reset to Defaults</span>
        </button>

        <button
          onClick={handleSave}
          disabled={isLoading}
          className="flex items-center space-x-2 px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 disabled:bg-orange-300 transition-all duration-150 transform hover:scale-105 active:scale-95 disabled:transform-none"
        >
          <Save className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>{isLoading ? 'Saving...' : 'Save Settings'}</span>
        </button>
      </div>

      {/* Password Change Modal */}
      <PasswordChangeModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        doctorId={doctorId}
      />

    </div>
  )
}
