'use client'

import { useState } from 'react'
import { <PERSON> } from '@/lib/types'
import { DashboardHeader } from '@/components/dashboard/dashboard-header'
import { TemplatesSidebar } from './templates-sidebar'
import { TemplateEditor } from './template-editor'
import { ComingSoonOverlay } from './coming-soon-overlay'

interface TemplatesInterfaceProps {
  user: Doctor | null
  doctorId: string
}

// Mock template type for UI
interface Template {
  id: string
  name: string
  content: string
  created_at: string
  updated_at: string
}

// Mock templates data
const mockTemplates: Template[] = [
  {
    id: '1',
    name: 'General Consultation',
    content: 'Pat<PERSON> presents with...\n\nChief Complaint:\n\nHistory of Present Illness:\n\nPhysical Examination:\n\nAssessment:\n\nPlan:',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2', 
    name: 'Follow-up Visit',
    content: 'Follow-up visit for...\n\nInterval History:\n\nCurrent Medications:\n\nReview of Systems:\n\nPhysical Examination:\n\nAssessment and Plan:',
    created_at: '2024-01-10T14:30:00Z',
    updated_at: '2024-01-12T09:15:00Z'
  },
  {
    id: '3',
    name: 'Discharge Summary',
    content: 'Discharge Summary\n\nAdmission Date:\nDischarge Date:\n\nPrimary Diagnosis:\nSecondary Diagnoses:\n\nHospital Course:\n\nDischarge Medications:\n\nFollow-up Instructions:',
    created_at: '2024-01-08T16:45:00Z',
    updated_at: '2024-01-08T16:45:00Z'
  }
]

export function TemplatesInterface({ user, doctorId: _doctorId }: TemplatesInterfaceProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(mockTemplates[0])
  const [templates] = useState<Template[]>(mockTemplates)

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 relative">
      {/* Header */}
      <DashboardHeader user={user} />

      {/* Main Content */}
      <main className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]">
            
            {/* Left Sidebar - Templates List */}
            <div className="lg:col-span-1">
              <TemplatesSidebar 
                templates={templates}
                selectedTemplate={selectedTemplate}
                onSelectTemplate={setSelectedTemplate}
                onNewTemplate={() => {
                  const newTemplate: Template = {
                    id: `new-${Date.now()}`,
                    name: 'New Template',
                    content: '',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  }
                  setSelectedTemplate(newTemplate)
                }}
              />
            </div>

            {/* Right Content - Template Editor */}
            <div className="lg:col-span-3">
              <TemplateEditor 
                template={selectedTemplate}
                onSave={(template) => {
                  // Mock save functionality
                  console.log('Saving template:', template)
                }}
              />
            </div>
          </div>
        </div>
      </main>

      {/* Coming Soon Overlay */}
      <ComingSoonOverlay />
    </div>
  )
}
