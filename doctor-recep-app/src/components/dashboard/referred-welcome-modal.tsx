'use client'

import React from 'react'
import { X, <PERSON>, Gift, Star, Crown } from 'lucide-react'
import { ReferralInfo } from '@/lib/types'

interface ReferredWelcomeModalProps {
  isOpen: boolean
  onClose: () => void
  referralInfo: ReferralInfo | null
}

export function ReferredWelcomeModal({ isOpen, onClose, referralInfo }: ReferredWelcomeModalProps) {
  if (!isOpen || !referralInfo?.referred_by) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-sm sm:max-w-md w-full mx-2 sm:mx-4 overflow-hidden max-h-[95vh] overflow-y-auto">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-teal-500 to-emerald-600 p-4 sm:p-6 text-white relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:text-gray-200 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="text-center">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
              <Gift className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <h2 className="text-xl sm:text-2xl font-bold mb-2">Welcome to Celer AI!</h2>
            <p className="text-sm sm:text-base text-emerald-100">You&apos;re part of a trusted network</p>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6">
          {/* Referral Info */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 sm:p-4 rounded-lg border border-blue-200 mb-4 sm:mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-blue-700 font-medium">Referred by</p>
                <p className="text-base sm:text-lg font-bold text-blue-900">
                  Dr. {referralInfo.referred_by.name}
                </p>
              </div>
            </div>
          </div>

          {/* Welcome Message */}
          <div className="text-center mb-4 sm:mb-6">
            <p className="text-sm sm:text-base text-gray-700 leading-relaxed">
              You&apos;re here because <span className="font-semibold text-teal-600">Dr. {referralInfo.referred_by.name}</span> trusts Celer AI.
              Happy Consulting! 🎉
            </p>
          </div>

          {/* Benefits */}
          <div className="space-y-2 sm:space-y-3 mb-4 sm:mb-6">
            <h3 className="text-sm sm:text-base font-semibold text-gray-900 text-center">What&apos;s included:</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                <span className="text-xs sm:text-sm text-gray-700">50 AI-powered consultations monthly</span>
              </div>
              <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                <Gift className="w-4 h-4 text-emerald-500 flex-shrink-0" />
                <span className="text-xs sm:text-sm text-gray-700">Advanced medical templates</span>
              </div>
              <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                <Crown className="w-4 h-4 text-purple-500 flex-shrink-0" />
                <span className="text-xs sm:text-sm text-gray-700">Priority support from our team</span>
              </div>
            </div>
          </div>

          {/* Referral Program Info */}
          <div className="bg-gradient-to-r from-amber-50 to-yellow-50 p-3 sm:p-4 rounded-lg border border-amber-200 mb-4 sm:mb-6">
            <h4 className="text-sm sm:text-base font-semibold text-amber-800 mb-2">Join the Referral Program!</h4>
            <p className="text-xs sm:text-sm text-amber-700 leading-relaxed">
              You can also refer colleagues and earn 10% off your next bill for each successful referral. 
              After 3 successful referrals, you&apos;ll become a <span className="font-semibold">Celer AI Ambassador</span>!
            </p>
          </div>

          {/* CTA */}
          <button
            onClick={onClose}
            className="w-full bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm sm:text-base"
          >
            Start Consulting
          </button>
        </div>
      </div>
    </div>
  )
}