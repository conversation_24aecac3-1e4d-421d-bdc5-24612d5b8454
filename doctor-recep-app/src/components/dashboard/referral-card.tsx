'use client'

import { useState, useEffect } from 'react'
import { Users, Gift, Crown, Share2, Co<PERSON>, Check, Trophy, Star } from 'lucide-react'
import { ReferralInfo } from '@/lib/types'
import { getReferralInfo, generateReferralLink } from '@/lib/actions/referrals'

interface ReferralCardProps {
  doctorId: string
}

export function ReferralCard({ doctorId }: ReferralCardProps) {
  const [referralInfo, setReferralInfo] = useState<ReferralInfo | null>(null)
  const [referralLink, setReferralLink] = useState<string>('')
  const [copied, setCopied] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      const [infoResult, linkResult] = await Promise.all([
        getReferralInfo(doctorId),
        generateReferralLink(doctorId)
      ])

      if (infoResult.success) {
        setReferralInfo(infoResult.data)
      }
      
      if (linkResult.success) {
        setReferralLink(linkResult.data)
      }
      
      setLoading(false)
    }

    fetchData()
  }, [doctorId])

  const handleCopyLink = async () => {
    if (referralLink) {
      await navigator.clipboard.writeText(referralLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const getBadgeDisplay = () => {
    if (!referralInfo) return null
    
    const { successful_referrals } = referralInfo
    
    if (successful_referrals >= 3) {
      return (
        <div className="flex items-center space-x-2 p-3 bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-lg">
          <div className="w-8 h-8 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full flex items-center justify-center">
            <Crown className="w-4 h-4 text-white" />
          </div>
          <div>
            <p className="text-sm font-semibold text-amber-800">Celer AI Ambassador</p>
            <p className="text-xs text-amber-600">Elite referral status achieved!</p>
          </div>
        </div>
      )
    }
    
    return null
  }

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-6 border border-orange-200/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!referralInfo) {
    return null
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-6 border border-orange-200/50 hover:shadow-xl transition-all duration-300">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-slate-800">Referral Program</h3>
        <div className="w-8 h-8 rounded-md bg-emerald-100 flex items-center justify-center shadow-sm">
          <Gift className="w-5 h-5 text-emerald-600" />
        </div>
      </div>

      {/* Badge Display */}
      {getBadgeDisplay()}

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-6 mt-4">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200/50">
          <div className="flex items-center space-x-2 mb-2">
            <Users className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-700 font-medium">Total Referrals</span>
          </div>
          <p className="text-2xl font-bold text-blue-800">{referralInfo.total_referrals}</p>
        </div>

        <div className="bg-gradient-to-r from-emerald-50 to-green-50 p-4 rounded-lg border border-emerald-200/50">
          <div className="flex items-center space-x-2 mb-2">
            <Trophy className="w-4 h-4 text-emerald-600" />
            <span className="text-sm text-emerald-700 font-medium">Successful</span>
          </div>
          <p className="text-2xl font-bold text-emerald-800">{referralInfo.successful_referrals}</p>
        </div>

        <div className="bg-gradient-to-r from-amber-50 to-yellow-50 p-4 rounded-lg border border-amber-200/50">
          <div className="flex items-center space-x-2 mb-2">
            <Star className="w-4 h-4 text-amber-600" />
            <span className="text-sm text-amber-700 font-medium">Pending</span>
          </div>
          <p className="text-2xl font-bold text-amber-800">{referralInfo.pending_referrals}</p>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200/50">
          <div className="flex items-center space-x-2 mb-2">
            <Gift className="w-4 h-4 text-purple-600" />
            <span className="text-sm text-purple-700 font-medium">Discount Earned</span>
          </div>
          <p className="text-2xl font-bold text-purple-800">₹{referralInfo.discount_earned.toFixed(0)}</p>
        </div>
      </div>

      {/* Referral Link */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Your Referral Link
        </label>
        <div className="flex space-x-2">
          <input
            type="text"
            value={referralLink}
            readOnly
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
          />
          <button
            onClick={handleCopyLink}
            className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-md text-sm flex items-center space-x-1 transition-colors"
          >
            {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
            <span>{copied ? 'Copied!' : 'Copy'}</span>
          </button>
        </div>
      </div>

      {/* Recent Referrals */}
      {referralInfo.recent_referrals.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-slate-700 mb-3">Recent Referrals</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {referralInfo.recent_referrals.slice(0, 5).map((referral) => (
              <div
                key={referral.id}
                className="flex items-center justify-between p-3 bg-slate-50 rounded-lg border border-slate-200"
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-800 truncate">
                    {referral.name}
                  </p>
                  <p className="text-xs text-slate-500">
                    {new Date(referral.signup_date).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <span
                    className={`inline-flex px-2 py-1 text-xs rounded-full font-medium ${
                      referral.status === 'converted'
                        ? 'bg-emerald-100 text-emerald-800'
                        : referral.status === 'pending'
                        ? 'bg-amber-100 text-amber-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {referral.status === 'converted' ? 'Paid' : 
                     referral.status === 'pending' ? 'Pending' : 'Expired'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Referral Info */}
      <div className="mt-6 p-4 bg-gradient-to-r from-teal-50 to-emerald-50 rounded-lg border border-teal-200/50">
        <div className="flex items-start space-x-2">
          <Share2 className="w-4 h-4 text-teal-600 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-teal-700">
            <p className="font-medium mb-1">How it works:</p>
            <ul className="text-xs space-y-1 text-teal-600">
              <li>• Share your referral link with colleagues</li>
              <li>• Get 10% off next month when they pay</li>
              <li>• Earn ambassador status after 3 successful referrals</li>
              <li>• Track your progress and rewards here</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Referred By Section */}
      {referralInfo.referred_by && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-700">
              You were referred by <span className="font-medium">{referralInfo.referred_by.name}</span>
            </span>
          </div>
        </div>
      )}
    </div>
  )
}