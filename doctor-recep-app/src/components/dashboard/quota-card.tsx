'use client'

import { useState, useEffect } from 'react'
import { Zap, Calendar, AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react'
import { QuotaInfo } from '@/lib/types'
import { QuotaWarningModal } from './quota-warning-modal'
import { createContactRequest, hasActiveContactRequest } from '@/lib/actions/contact-requests'

interface QuotaCardProps {
  quota: QuotaInfo
  doctorId: string
}

export function QuotaCard({ quota, doctorId }: QuotaCardProps) {
  const [showWarningModal, setShowWarningModal] = useState(false)
  const [isRequesting, setIsRequesting] = useState(false)
  const [hasRequested, setHasRequested] = useState(false)

  useEffect(() => {
    // Show warning modal when approaching or exceeding limits
    if (quota.quota_percentage >= 80) {
      setShowWarningModal(true)
    }

    // Check if user has already made a contact request
    const checkContactRequest = async () => {
      const result = await hasActiveContactRequest(doctorId)
      if (result.success && result.data) {
        setHasRequested(true)
      }
    }
    checkContactRequest()
  }, [quota.quota_percentage, doctorId])

  const handleContactFounder = async () => {
    setIsRequesting(true)
    try {
      const result = await createContactRequest(doctorId, `Quota upgrade request - Currently at ${quota.quota_percentage}% (${quota.quota_used}/${quota.monthly_quota} consultations used)`)
      if (result.success) {
        setHasRequested(true)
      }
    } catch (error) {
      console.error('Error creating contact request:', error)
    } finally {
      setIsRequesting(false)
    }
  }

  const getQuotaColor = () => {
    if (quota.quota_percentage >= 90) return 'text-red-600'
    if (quota.quota_percentage >= 70) return 'text-orange-600'
    return 'text-emerald-600'
  }

  const getQuotaBgColor = () => {
    if (quota.quota_percentage >= 90) return 'bg-red-100'
    if (quota.quota_percentage >= 70) return 'bg-orange-100'
    return 'bg-emerald-100'
  }

  const getQuotaIcon = () => {
    if (quota.quota_percentage >= 90) return AlertTriangle
    if (quota.quota_percentage >= 70) return Zap
    return CheckCircle
  }

  const getProgressBarColor = () => {
    if (quota.quota_percentage >= 90) return 'bg-red-500'
    if (quota.quota_percentage >= 70) return 'bg-orange-500'
    return 'bg-emerald-500'
  }

  const QuotaIcon = getQuotaIcon()

  return (
    <div className="bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-4 border border-orange-200/50 hover:shadow-xl transition-all duration-300 h-full flex flex-col">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-slate-800">Consultations</h3>
        <div className={`w-6 h-6 rounded-lg ${getQuotaBgColor()} flex items-center justify-center shadow-md`}>
          <QuotaIcon className={`w-4 h-4 ${getQuotaColor()}`} />
        </div>
      </div>

      {/* Main Quota Display */}
      <div className="mb-4 flex-1 space-y-3">
        <div className="text-center bg-gradient-to-br from-slate-50 to-orange-50 rounded-lg p-3 sm:p-4">
          <div className="text-xs sm:text-sm text-slate-600 mb-1">Monthly Usage</div>
          <div className="text-base sm:text-lg font-bold text-slate-800">
            {quota.quota_used} / {quota.monthly_quota}
          </div>
          <div className="w-full bg-orange-100 rounded-full h-2 sm:h-2.5 mt-2">
            <div
              className={`h-2 sm:h-2.5 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
              style={{ width: `${Math.min(quota.quota_percentage, 100)}%` }}
            />
          </div>
          <div className="mt-1">
            <span className={`text-sm sm:text-base font-bold ${getQuotaColor()}`}>
              {quota.quota_percentage}%
            </span>
          </div>
        </div>

        {/* Split Info Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-2 sm:p-3 text-center">
            <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-teal-600 mx-auto mb-1" />
            <div className="text-xs sm:text-sm text-slate-600">Remaining</div>
            <div className="text-sm sm:text-base font-bold text-slate-800">{quota.quota_remaining}</div>
          </div>
          
          <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-2 sm:p-3 text-center">
            <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mx-auto mb-1" />
            <div className="text-xs sm:text-sm text-slate-600">Resets in</div>
            <div className="text-sm sm:text-base font-bold text-slate-800">{quota.days_until_reset} day{quota.days_until_reset !== 1 ? 's' : ''}</div>
          </div>
        </div>
      </div>

      {/* Warning Messages */}
      {quota.quota_percentage >= 90 && (
        <div className="p-2 bg-red-50 border border-red-200 rounded">
          <div className="flex items-start">
            <AlertTriangle className="w-3 h-3 text-red-500 mt-0.5 mr-1 flex-shrink-0" />
            <div className="text-xs text-red-700">
              <p className="font-medium">Quota almost exhausted!</p>
              <p>Contact admin to increase limit.</p>
            </div>
          </div>
        </div>
      )}

      {quota.quota_percentage >= 70 && quota.quota_percentage < 90 && (
        <div className="p-2 bg-orange-50 border border-orange-200 rounded">
          <div className="flex items-start">
            <Zap className="w-3 h-3 text-orange-500 mt-0.5 mr-1 flex-shrink-0" />
            <div className="text-xs text-orange-700">
              <p className="font-medium">High usage detected</p>
              <p>Monitor AI generation usage.</p>
            </div>
          </div>
        </div>
      )}

      {/* Quota Warning Modal */}
      <QuotaWarningModal
        isOpen={showWarningModal}
        onClose={() => setShowWarningModal(false)}
        quotaInfo={quota}
        onContactFounder={handleContactFounder}
        isRequesting={isRequesting}
        hasRequested={hasRequested}
      />
    </div>
  )
}