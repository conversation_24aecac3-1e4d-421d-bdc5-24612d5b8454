'use client'

import { useState, useEffect } from 'react'
import { Users, Gift, Crown, Copy, Check } from 'lucide-react'
import { ReferralInfo } from '@/lib/types'
import { getReferralInfo, generateReferralLink } from '@/lib/actions/referrals'

interface ReferralStatsProps {
  doctorId: string
}

export function ReferralStats({ doctorId }: ReferralStatsProps) {
  const [referralInfo, setReferralInfo] = useState<ReferralInfo | null>(null)
  const [referralLink, setReferralLink] = useState<string>('')
  const [copied, setCopied] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      const [infoResult, linkResult] = await Promise.all([
        getReferralInfo(doctorId),
        generateReferralLink(doctorId)
      ])

      if (infoResult.success) {
        setReferralInfo(infoResult.data)
      }
      
      if (linkResult.success) {
        setReferralLink(linkResult.data)
      }
      
      setLoading(false)
    }

    fetchData()
  }, [doctorId])

  const handleCopyLink = async () => {
    if (referralLink) {
      await navigator.clipboard.writeText(referralLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm shadow border border-orange-200/50 rounded-lg p-4 h-full">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    )
  }

  if (!referralInfo) {
    return null
  }

  const getBadgeIcon = () => {
    if (referralInfo.successful_referrals >= 3) {
      return <Crown className="w-5 h-5 text-amber-500" />
    }
    return <Gift className="w-5 h-5 text-emerald-500" />
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-4 border border-orange-200/50 hover:shadow-xl transition-all duration-300 h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {getBadgeIcon()}
          <h3 className="text-sm font-medium text-slate-800">Referral Program</h3>
        </div>
        <div className="w-6 h-6 rounded-lg bg-emerald-100 flex items-center justify-center shadow-md">
          {getBadgeIcon()}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="flex-1 space-y-3">
        {/* Main Stats Grid */}
        <div className="grid grid-cols-2 gap-2">
          <div className="bg-gradient-to-br from-emerald-50 to-green-50 p-3 rounded-lg text-center">
            <div className="text-xs text-emerald-700 font-medium mb-1">Success</div>
            <div className="text-lg font-bold text-emerald-800">{referralInfo.successful_referrals}</div>
          </div>
          <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-3 rounded-lg text-center">
            <div className="text-xs text-purple-700 font-medium mb-1">Earned</div>
            <div className="text-sm font-bold text-purple-800">₹{referralInfo.discount_earned.toFixed(0)}</div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div className="bg-gradient-to-br from-blue-50 to-cyan-50 p-3 rounded-lg text-center">
            <div className="text-xs text-blue-700 font-medium mb-1">Total</div>
            <div className="text-lg font-bold text-blue-800">{referralInfo.total_referrals}</div>
          </div>
          <div className="bg-gradient-to-br from-amber-50 to-yellow-50 p-3 rounded-lg text-center">
            <div className="text-xs text-amber-700 font-medium mb-1">Pending</div>
            <div className="text-lg font-bold text-amber-800">{referralInfo.pending_referrals}</div>
          </div>
        </div>

        {/* Referral Link */}
        <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-3 rounded-lg">
          <div className="text-xs text-slate-600 mb-2 text-center">Your Referral Link</div>
          <div className="flex space-x-2">
            <input
              type="text"
              value={referralLink}
              readOnly
              className="flex-1 px-2 py-1 rounded text-xs bg-white/70 text-slate-800 focus:outline-none"
            />
            <button
              onClick={handleCopyLink}
              className="px-3 py-1 bg-teal-600 hover:bg-teal-700 text-white rounded text-xs transition-colors font-medium"
            >
              {copied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
            </button>
          </div>
        </div>

        {/* Referred By Section */}
        {referralInfo.referred_by && (
          <div className="p-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
            <div className="flex items-center justify-center space-x-2">
              <Users className="w-3 h-3 text-blue-600" />
              <span className="text-xs text-blue-700">
                Referred by <span className="font-medium">{referralInfo.referred_by.name}</span>
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}