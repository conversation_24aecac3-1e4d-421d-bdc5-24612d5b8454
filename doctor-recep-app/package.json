{"name": "doctor-recep-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3004", "build": "next build --turbopack", "start": "next start -p 3004", "lint": "next lint", "create-admin": "node scripts/create-admin.js", "test-quota": "node tests/quota-system-test.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.800.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tiptap/extension-placeholder": "^2.13.0", "@tiptap/react": "^2.13.0", "@tiptap/starter-kit": "^2.13.0", "@vercel/analytics": "^1.5.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "form-data": "^4.0.2", "framer-motion": "^12.16.0", "jose": "^6.0.11", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "readline": "^1.3.0", "tailwind-merge": "^3.3.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5", "wrangler": "^4.19.1"}}